<script setup lang="ts">
import { expoListApi, changeExpoStatusApi, deleteExpoApi } from '@/api';
import { useRouter } from 'vue-router';
import { useAdminCommonStore } from '@/stores/adminCommon';
const store = useAdminCommonStore();
const router = useRouter();
//分页
const total = ref(0);
const loading = ref(false);
const reqForm = reactive({
  merchantGuid: '',
  showStatus: true,
  pageSize: 10,
  page: 1,
});
let merchantGuid = computed(() => {
  return store.getTenantInfo.guid;
});
let goodsList = ref([]);
//获取产品列表
const getList = async () => {
  loading.value = true;
  reqForm.merchantGuid = merchantGuid.value;
  let goodsRes = await expoListApi(reqForm);
  loading.value = false;
  total.value = goodsRes.data.total;
  goodsList.value = goodsRes.data.data;
};
const showStatusList = reactive([
  {
    id: '',
    name: '全部',
  },
  {
    id: true,
    name: '展示中',
  },
  {
    id: false,
    name: '未展示',
  },
]);
// 添加切换展示状态的方法
const onChangeShowStatus = (value) => {
  reqForm.showStatus = value;
  getList();
};
const handlePageChang = async (page) => {
  reqForm.page = page;
  getList();
};
const onEdit = (item) => {
  router.push({
    name: 'setExpoInfo',
    query: {
      guid: item.guid,
      type: 'EDIT',
    },
  });
};
const onAdd = () => {
  router.push({
    name: 'setExpoInfo',
    query: {
      type: 'ADD',
    },
  });
};
const onSetQuestion = (item) => {
  router.push({
    name: 'expoFaqList',
    query: {
      guid: item.guid,
      merchantGuid: 'e108201b02ae42e686bcc4c302cbbd11',
    },
  });
};
const onSetBanner = (item) => {
  router.push({
    name: 'expoBannerList',
    query: {
      guid: item.guid,
      merchantGuid: 'e108201b02ae42e686bcc4c302cbbd11',
    },
  });
};

const onDisable = async (item) => {
  let req = {
    guid: item.guid,
  };
  await changeExpoStatusApi(req);
  ElMessage.success('修改状态成功');
  getList();
};
const onSetAdmin = (item) => {
  router.push({
    name: 'expoAdmin',
    query: {
      guid: item.guid,
    },
  });
};
getList();
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main v-loading="loading">
        <div class="hearder-box">
          <el-form :inline="true" :model="reqForm" class="demo-form-inline">
            <el-form-item>
              <el-button type="primary" @click="onAdd">新增</el-button>
            </el-form-item>
            <el-form-item label="展示状态">
              <div class="mode-box">
                <div :class="['item', { active: reqForm.showStatus === item.id }]"
                  v-for="(item, index) in showStatusList" :key="index" @click="onChangeShowStatus(item.id)">
                  {{ item.name }}
                </div>
              </div>
            </el-form-item>
          </el-form>
        </div>
        <el-table :data="goodsList" border style="width: 100%">
          <el-table-column prop="sysId" label="sysId" width="80" />
          <el-table-column prop="showOrder" label="排序" width="100" />
          <el-table-column prop="name" label="展会名称" width="200" />
          <el-table-column prop="startTime" label="展会开始时间/截止时间" width="180">
            <template #default="scope"> {{ scope.row.startTime }} - {{ scope.row.endTime }} </template>
          </el-table-column>
          <el-table-column prop="createTime" label="展会创建时间" width="200"> </el-table-column>
          <el-table-column prop="aiPoint" label="AI点数余额" width="260">
            <template #default="scope">
              {{ scope.row.aiPoint }}
              <span style="color: #ccc; margin-left: 10px">{{
                scope.row.aiPoint < 200 ? '点数不足，请及时充值' : '' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="showStatus" label="展示状态" width="160">
            <template #default="scope">
              {{ scope.row.showStatus ? '展示中' : '未展示' }}
              <p style="margin-top: 5px; color: #ccc">{{ scope.row.closeReason }}</p>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" min-width="350">
            <template #default="scope">
              <el-button size="small" type="primary" @click="onEdit(scope.row)">编辑</el-button>
              <el-popconfirm confirm-button-text="确认" cancel-button-text="取消" icon-color="red"
                :title="scope.row.status == 2 ? '是否启用展会' : '是否禁用展会'" @confirm="onDisable(scope.row)">
                <template #reference>
                  <el-button size="small" :type="scope.row.status == 2 ? 'success' : 'danger'">{{
                    scope.row.status == 2 ? '启用' : '禁用'
                  }}</el-button>
                </template>
              </el-popconfirm>
              <el-button size="small" type="primary" @click="onSetQuestion(scope.row)">常见问题设置</el-button>
              <el-button size="small" type="primary" @click="onSetBanner(scope.row)">轮播图设置</el-button>
              <el-button size="small" type="primary" @click="onSetAdmin(scope.row)">管理员设置</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-main>
      <el-footer>
        <el-pagination background layout="prev,pager, next" :total="total" :current-page="reqForm.page"
          @current-change="handlePageChang" />
      </el-footer>
    </el-container>
  </div>
</template>

<style scoped lang="scss">
.mode-box {
  display: flex;

  .item {
    padding: 0px 15px;
    border: 1px solid #dcdfe6;
    cursor: pointer;
    margin-right: -1px;

    &:first-child {
      border-radius: 4px 0 0 4px;
    }

    &:last-child {
      border-radius: 0 4px 4px 0;
    }

    &.active {
      color: #409eff;
      border-color: #409eff;
      background-color: #ecf5ff;
      z-index: 1;
    }
  }
}

.upload-img-box {
  width: 120px;
  height: 120px;
  border: 1px solid #cdd0d6;
  box-sizing: border-box;
  position: relative;
  border-radius: 6px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;

  .preview-img {
    width: 100%;
    height: 100%;
  }

  .operate-box {
    position: absolute;
    z-index: 9;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.5);
    visibility: hidden;
  }

  &:hover .operate-box {
    visibility: visible;
  }
}
</style>
