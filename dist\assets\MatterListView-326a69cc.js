import{d as _e,a as f,r as h,c,b as e,w as o,h as l,aL as me,e as fe,C as ye,o as n,f as b,i as p,G as M,H as z,aC as ge,U as L,T,I as C,ah as A,ai as F,F as q,E as _,J as he,aM as ve,aN as be,aO as we,aP as Te,p as Ce,l as ke,q as Ee,s as Ue,N as xe,af as Ve,v as Ie,y as De,x as Se,Y as Ne,aI as Me,am as ze,m as Le,n as Ae,k as Fe,O as qe,P as Be,Z as Pe,_ as Re}from"./index-6e8b0ade.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                  *//* empty css                    */import{_ as je,a as $e}from"./plus-19e9063c.js";/* empty css                 *//* empty css               *//* empty css                  *//* empty css                       *//* empty css                        *//* empty css                      *//* empty css                        *//* empty css                   *//* empty css                        *//* empty css                     */const Oe={class:"header-box"},Ge={class:"mode-box"},He=["onClick"],Je=["src"],Ye={key:0,class:"upload-img-box"},Ze=["src"],Ke={key:1,class:"upload-btn"},Qe={class:"operate-box"},We={key:1,class:"upload-img-box"},Xe=["src"],eo={key:1,class:"upload-btn"},oo={class:"operate-box"},to=_e({__name:"MatterListView",setup(ao){const B=f([{id:0,type:"",name:"全部"},{id:1,type:"image",name:"图片"},{id:2,type:"video",name:"视频"}]),P=h(!1);let k=h("");const E=h(0),y=f({resourceType:"",page:1,pageSize:13}),U=h([]),R=async s=>{y.page=s,g()},g=async()=>{let s=await me(y);U.value=s.data.data,E.value=s.data.total,console.log(s)},d=f({dialogtype:"ADD",isShow:!1}),j=()=>{d.isShow=!0,d.dialogtype="ADD"},$=f([{label:"数字人",value:"shuziren"},{label:"海报",value:"poster"}]);let t=f({sysId:0,resourceType:"image",useType:"shuziren",resourceName:"",resourceUrl:""});const x=h(),O=f({resourceName:[{required:!0,message:"请输入素材名称",trigger:"change"}],resourceUrl:[{required:!0,message:"上传素材",trigger:"change"}]}),G=()=>{t.resourceUrl=""},H=s=>{if(t.resourceType==="image"){let a=["image/jpg","image/png","image/jpeg"];if(!a.includes(s.type))return _.warning("当前图片仅支持格式为："+a.join(" ，")),!1}else if(t.resourceType==="video"){let a=["video/mp4"];if(!a.includes(s.type))return _.warning("当前视频仅支持格式为："+a.join(" ，")),!1}return!0},J=async s=>{const a=new FormData;if(t.resourceType==="image"){a.append("img",s.file);let i=await he(a);t.resourceUrl=i.data}else if(t.resourceType==="video"){a.append("video",s.file);let i=await ve(a);t.resourceUrl=i.data}},V=()=>{t.resourceUrl=""},Y=async s=>{s.validate(async a=>{if(a)try{d.dialogtype==="ADD"?(await be(t),_.success("新增成功")):d.dialogtype==="EDIT"&&(await we(t),_.success("修改成功")),s.resetFields(),d.isShow=!1,g()}catch(i){throw _.error(i),new Error(i)}})},Z=s=>{k.value=s,y.resourceType=s},K=()=>{g()},Q=s=>{t.sysId=s.sysId,t.resourceType=s.resourceType,t.resourceName=s.resourceName,t.resourceUrl=s.resourceUrl,t.useType=s.useType,d.isShow=!0,d.dialogtype="EDIT"},W=async s=>{try{let a=s.sysId;await Te({sysId:a}),g(),_.success("删除成功")}catch(a){_.success(a)}};return g(),(s,a)=>{const i=Ce,u=ke,I=Ee,m=Ue,X=xe,ee=Ve,oe=Ie,te=De,ae=Se,le=Ne,se=fe,D=Me,re=ze,ne=Le,ie=Ae,ce=Fe,S=je,v=qe,N=$e,de=Be,ue=ye,pe=Pe;return n(),c("div",null,[e(se,{class:"wrapper"},{default:o(()=>[e(te,null,{default:o(()=>[b("div",Oe,[e(I,{inline:!0,model:l(y),class:"demo-form-inline"},{default:o(()=>[e(u,null,{default:o(()=>[e(i,{type:"primary",onClick:j},{default:o(()=>[p("新增素材")]),_:1})]),_:1}),e(u,{label:"素材类型"},{default:o(()=>[b("div",Ge,[(n(!0),c(M,null,z(l(B),(r,w)=>(n(),c("div",{class:ge(["item",{active:l(k)===r.type}]),key:w,onClick:lo=>Z(r.type)},L(r.name),11,He))),128))])]),_:1}),e(u,null,{default:o(()=>[e(i,{type:"primary",onClick:K},{default:o(()=>[p("搜索")]),_:1})]),_:1})]),_:1},8,["model"])]),T((n(),C(oe,{data:l(U),border:"",style:{width:"100%"}},{default:o(()=>[e(m,{prop:"sysId",label:"id",width:"80"}),e(m,{prop:"resourceName",label:"素材名称"}),e(m,{prop:"resourceType",label:"素材类型"},{default:o(r=>[p(L(r.row.resourceType==="image"?"图片":"视频"),1)]),_:1}),e(m,{prop:"resourceUrl",label:"素材",width:"260"},{default:o(r=>[r.row.resourceType==="image"?(n(),C(X,{key:0,src:r.row.resourceUrl,style:{height:"100px"}},null,8,["src"])):(n(),c("video",{key:1,src:r.row.resourceUrl,width:"200",height:"100",controls:"",preload:"none"},null,8,Je))]),_:1}),e(m,{prop:"modifyTime",label:"时间",width:"250"}),e(m,{label:"操作"},{default:o(r=>[e(i,{size:"small",type:"primary",onClick:w=>Q(r.row)},{default:o(()=>[p("编辑")]),_:2},1032,["onClick"]),e(ee,{"confirm-button-text":"确认","cancel-button-text":"取消","icon-color":"red",title:"是否删除该素材?",onConfirm:w=>W(r.row)},{reference:o(()=>[e(i,{size:"small",type:"danger"},{default:o(()=>[p("删除")]),_:1})]),_:2},1032,["onConfirm"])]),_:1})]),_:1},8,["data"])),[[pe,l(P)]])]),_:1}),e(le,null,{default:o(()=>[e(ae,{background:"",layout:"prev,pager, next",total:l(E),"current-page":l(y).page,onCurrentChange:R},null,8,["total","current-page"])]),_:1})]),_:1}),e(ue,{modelValue:l(d).isShow,"onUpdate:modelValue":a[4]||(a[4]=r=>l(d).isShow=r),title:"创建/修改 素材",width:"600px"},{default:o(()=>[e(I,{ref_key:"addForm",ref:x,model:l(t),class:"demo-form-inline","label-width":"100px",rules:l(O)},{default:o(()=>[e(u,{label:"素材类型",prop:"resourceType",onChange:G},{default:o(()=>[e(re,{modelValue:l(t).resourceType,"onUpdate:modelValue":a[0]||(a[0]=r=>l(t).resourceType=r)},{default:o(()=>[e(D,{label:"image"},{default:o(()=>[p("图片")]),_:1}),e(D,{label:"video"},{default:o(()=>[p("视频")]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"所属功能",prop:"useType"},{default:o(()=>[e(ie,{modelValue:l(t).useType,"onUpdate:modelValue":a[1]||(a[1]=r=>l(t).useType=r),class:"m-2",placeholder:"Select",size:"large",style:{width:"240px"}},{default:o(()=>[(n(!0),c(M,null,z(l($),r=>(n(),C(ne,{key:r.value,label:r.label,value:r.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"素材名称",prop:"resourceName"},{default:o(()=>[e(ce,{modelValue:l(t).resourceName,"onUpdate:modelValue":a[2]||(a[2]=r=>l(t).resourceName=r),placeholder:"名称"},null,8,["modelValue"])]),_:1}),e(u,{label:"上传素材",prop:"resourceUrl"},{default:o(()=>[e(de,{ref:"uploadImgRef",class:"avatar-uploader","before-upload":H,"show-file-list":!1,"http-request":J},{default:o(()=>[l(t).resourceType==="image"?(n(),c("div",Ye,[l(t).resourceUrl?(n(),c("img",{key:0,src:l(t).resourceUrl,class:"preview-img"},null,8,Ze)):(n(),c("div",Ke,[e(v,{size:"30",color:"#cdd0d6"},{default:o(()=>[e(S)]),_:1})])),T(b("div",Qe,[e(v,{size:"22",color:"#ffffff",onClick:F(V,["stop"])},{default:o(()=>[e(N)]),_:1},8,["onClick"])],512),[[A,l(t).resourceUrl]])])):q("",!0),l(t).resourceType==="video"?(n(),c("div",We,[l(t).resourceUrl?(n(),c("video",{key:0,src:l(t).resourceUrl,wdith:"120",height:"120"},null,8,Xe)):(n(),c("div",eo,[e(v,{size:"30",color:"#cdd0d6"},{default:o(()=>[e(S)]),_:1})])),T(b("div",oo,[e(v,{size:"22",color:"#ffffff",onClick:F(V,["stop"])},{default:o(()=>[e(N)]),_:1},8,["onClick"])],512),[[A,l(t).resourceUrl]])])):q("",!0)]),_:1},512)]),_:1}),e(u,null,{default:o(()=>[e(i,{type:"primary",onClick:a[3]||(a[3]=r=>Y(l(x)))},{default:o(()=>[p("提交")]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}});const Co=Re(to,[["__scopeId","data-v-847a2ab1"]]);export{Co as default};
