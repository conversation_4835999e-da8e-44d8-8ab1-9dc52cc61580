<script setup lang="ts">
import { KnowledgeSearchTestApi } from '@/api';
import { useRoute } from 'vue-router';

const route = useRoute();
const guid = route.query.guid;
const dialogIsShow = ref(false);
const searchText = ref('');
const searchList: any = ref([]);
const onTest = async () => {
  if (searchText.value.trim().length === 0) {
    ElMessage.warning('请输入源文本');
    return;
  }
  let res = await KnowledgeSearchTestApi({
    guid: guid,
    keywords: searchText.value,
  });
  res.data.records.forEach((element) => {
    element.scoreFormat = element.score.toFixed(2);
  });
  searchList.value = res.data.records;
};
interface FileItem {
  word_count: number;
  enabled: boolean;
  content: string;
  keywords: string[];
  index_node_hash: string;
  hit_count: number;
}
let fileItem = reactive<FileItem>({
  word_count: 0,
  enabled: false,
  content: '',
  keywords: [],
  index_node_hash: '',
  hit_count: 0,
});
const onText = (item: any) => {
  dialogIsShow.value = true;
  fileItem = { fileItem, ...item.segment };
};
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main>
        <div class="test-title">
          <div class="h1">召回测试</div>
          <div class="h2">基于给定的查询文本测试知识库的召回效果。</div>
        </div>
        <el-row :gutter="24">
          <el-col :span="12">
            <div class="test-input-box">
              <div class="title-box">
                <h6>源文本</h6>
              </div>
              <div class="content">
                <el-input v-model="searchText" :input-style="{ width: '100%' }" :rows="18"
                  placeholder="请输入文本，建议使用简短的陈述句" type="textarea" show-word-limit maxlength="200" resize="none" />
              </div>
              <div class="footer">
                <el-button type="primary" @click="onTest">测试</el-button>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="result-box">
              <div class="list-box">
                <div class="item" @click="onText(item)" v-for="item in searchList" :key="item.segment.id">
                  <el-progress :percentage="item.score.toFixed(2) * 100"
                    :format="(percentage) => (percentage / 100).toString()" color="#d0d5dd" />
                  <div class="content">
                    {{ item.segment.content }}
                  </div>
                  <!-- <div class="title">{{ item.segment.document.name }}</div> -->
                </div>
              </div>
              <div class="tip" v-if="searchList.length == 0">召回测试结果将展示在这里</div>
            </div>
          </el-col>
        </el-row>
      </el-main>
    </el-container>
    <el-dialog v-model="dialogIsShow" title="文档段落" width="600">
      <div class="text-paragraph-box">
        <div class="content">
          <el-input :input-style="{ width: '100%' }" v-model="fileItem.content" :rows="20" disabled type="textarea"
            resize="none" />
        </div>
        <div class="keywords">
          <div class="title"><el-text size="small">关键词</el-text></div>
          <div class="item-box">
            <el-tag v-for="(item, index) in fileItem.keywords" :key="index">{{ item }}</el-tag>
          </div>
        </div>
        <div class="detail">
          <el-text class="mx-1" size="small">{{ fileItem.word_count }}字符</el-text>
          <el-text class="mx-1" size="small">{{ fileItem.hit_count }}召回次数</el-text>
          <el-text class="mx-3" size="small" truncated>向量哈希:{{ fileItem.index_node_hash }}</el-text>
          <div>
            <el-text class="status" size="small">{{ fileItem.enabled ? '已启用' : '未启用' }}</el-text><el-switch
              v-model="fileItem.enabled" disabled />
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
:deep(.el-textarea.is-disabled .el-textarea__inner) {
  background-color: inherit;
  /* 继承父元素背景色 */
  color: inherit;
  /* 继承父元素字体颜色 */
  border-color: inherit;
  /* 继承父元素边框颜色 */
  cursor: auto;
  /* 鼠标指针变为禁用状态的样式 */
}

:deep(.el-dialog__header) {
  padding: 20px 20px 5px 20px;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-progress__text) {
  min-width: 0px;
}

.text-paragraph-box {
  .keywords {
    .title {
      margin-bottom: 10px;
      margin-top: 20px;
    }

    .item-box {
      display: flex;
      grid-gap: 0.5rem;
      gap: 0.5rem;
      flex-wrap: wrap;
    }
  }

  .detail {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #d7d7d7;
    display: flex;
    justify-content: space-between;

    .mx-1 {
      width: 80px;
    }

    .mx-3 {
      width: 280px;
    }

    .status {
      margin-right: 10px;
    }
  }
}

.wrapper {
  height: 86vh;
}

.result-box {
  height: 100%;

  .list-box {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;

    .item {
      width: 48%;
      background-color: #f9fafb;
      padding: 14px;
      border-radius: 14px;
      box-sizing: border-box;
      cursor: pointer;

      .content {
        height: 80px;
        font-size: 14px;
        width: 100%;
        margin-top: 10px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 4;
        /* 设置最大显示行数为4 */
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 1.4em;
      }
    }
  }

  .tip {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.divider {
  height: 100%;
  width: 1px;
  background-color: #cccccc;
}

.test-title {
  .h1 {
    font-size: 20px;
  }

  .h2 {
    font-size: 14px;
    margin-top: 10px;
  }
}

.test-input-box {
  position: relative;
  border-radius: 20px;
  border: 1px solid #155eef;
  margin-top: 20px;
  overflow: hidden;

  .title-box {
    background-color: #eef4ff;
    padding: 10px;

    h6 {
      margin: 0 auto;
    }
  }

  .footer {
    padding: 10px 20px;
    display: flex;
    justify-content: end;
  }
}

:deep(.el-textarea .el-input__count) {
  bottom: -30px;
  left: 10px;
  background-color: transparent;
  width: 100px;
}

:deep(.el-textarea__inner) {
  box-shadow: none;
}

:deep(.el-textarea__inner:hover) {
  box-shadow: none;
}

:deep(.el-textarea__inner:focus) {
  box-shadow: none;
}
</style>
