<script setup lang="ts">
import {
  createMaterialApi,
  updateMaterialApi,
  deleteMaterialApi,
  getMaterialListApi,
  uploadImg,
  uploadVideo,
} from '@/api/index';
import type { FormInstance, FormRules, UploadProps } from 'element-plus';
const modeList = reactive([
  {
    id: 0,
    type: '',
    name: '全部',
  },
  {
    id: 1,
    type: 'image',
    name: '图片',
  },
  {
    id: 2,
    type: 'video',
    name: '视频',
  },
]);
const loading = ref(false);
let modeStatus = ref('');
//分页
const total = ref(0);
const getListReq = reactive({
  resourceType: '',
  page: 1,
  pageSize: 13,
});
const materialList = ref([]);
const handlePageChang = async (page) => {
  getListReq.page = page;
  getMaterialList();
};
const getMaterialList = async () => {
  let res = await getMaterialListApi(getListReq);
  materialList.value = res.data.data;
  total.value = res.data.total;
  console.log(res);
};

const dialogConfig = reactive({
  dialogtype: 'ADD',
  isShow: false,
});
const onAddScene = () => {
  dialogConfig.isShow = true;
  dialogConfig.dialogtype = 'ADD';
};
const useType = reactive([
  { label: '数字人', value: 'shuziren' },
  { label: '海报', value: 'poster' },
]);
let fromReq = reactive({
  sysId: 0,
  resourceType: 'image',
  useType: 'shuziren',
  resourceName: '',
  resourceUrl: '',
});
const addForm = ref<FormInstance>();
const rules = reactive<FormRules>({
  resourceName: [{ required: true, message: '请输入素材名称', trigger: 'change' }],
  resourceUrl: [{ required: true, message: '上传素材', trigger: 'change' }],
});
const onChangeType = () => {
  fromReq.resourceUrl = '';
};
const beforeAvatarUpload: UploadProps['beforeUpload'] = (rawFile) => {
  if (fromReq.resourceType === 'image') {
    let fileTypes = ['image/jpg', 'image/png', 'image/jpeg'];
    if (!fileTypes.includes(rawFile.type)) {
      ElMessage.warning('当前图片仅支持格式为：' + fileTypes.join(' ，'));
      return false;
    }
  } else if (fromReq.resourceType === 'video') {
    let fileTypes = ['video/mp4'];
    if (!fileTypes.includes(rawFile.type)) {
      ElMessage.warning('当前视频仅支持格式为：' + fileTypes.join(' ，'));
      return false;
    }
  }

  // else if (rawFile.size / 1024 / 1024 > 2) {
  //   ElMessage.error('图片必须小于2M');
  //   return false;
  // }
  return true;
};
const upload = async (file) => {
  const formData = new FormData();
  // 添加要上传的文件名和文件对象,key就是你上传文件的字段名
  if (fromReq.resourceType === 'image') {
    formData.append('img', file.file);
    let res = await uploadImg(formData);
    fromReq.resourceUrl = res.data;
  } else if (fromReq.resourceType === 'video') {
    formData.append('video', file.file);
    let res = await uploadVideo(formData);
    fromReq.resourceUrl = res.data;
  }
};
const handleRemove = () => {
  fromReq.resourceUrl = '';
};
const onSubmit = async (formEl) => {
  formEl.validate(async (valid) => {
    if (!valid) {
      return;
    }
    try {
      if (dialogConfig.dialogtype === 'ADD') {
        await createMaterialApi(fromReq);
        ElMessage.success('新增成功');
      } else if (dialogConfig.dialogtype === 'EDIT') {
        await updateMaterialApi(fromReq);
        ElMessage.success('修改成功');
      }
      formEl.resetFields();
      dialogConfig.isShow = false;
      getMaterialList();
    } catch (error: any) {
      ElMessage.error(error);
      throw new Error(error);
    }
  });
};
const onChangeMode = (value) => {
  modeStatus.value = value;
  getListReq.resourceType = value;
};
//搜索
const onSearch = () => {
  getMaterialList();
};
const onEdit = (item) => {
  fromReq.sysId = item.sysId;
  fromReq.resourceType = item.resourceType;
  fromReq.resourceName = item.resourceName;
  fromReq.resourceUrl = item.resourceUrl;
  fromReq.useType = item.useType;
  dialogConfig.isShow = true;
  dialogConfig.dialogtype = 'EDIT';
};
const handleDelete = async (item) => {
  try {
    let sysId = item.sysId;
    await deleteMaterialApi({ sysId });
    getMaterialList();
    ElMessage.success('删除成功');
  } catch (error: any) {
    ElMessage.success(error);
  }
};

getMaterialList();
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main>
        <div class="header-box">
          <el-form :inline="true" :model="getListReq" class="demo-form-inline">
            <el-form-item><el-button type="primary" @click="onAddScene">新增素材</el-button></el-form-item>
            <el-form-item label="素材类型">
              <div class="mode-box">
                <div :class="['item', { active: modeStatus === item.type }]" v-for="(item, index) in modeList"
                  :key="index" @click="onChangeMode(item.type)">
                  {{ item.name }}
                </div>
              </div>
            </el-form-item>
            <el-form-item> <el-button type="primary" @click="onSearch">搜索</el-button></el-form-item>
          </el-form>
        </div>
        <el-table v-loading="loading" :data="materialList" border style="width: 100%">
          <el-table-column prop="sysId" label="id" width="80" />
          <el-table-column prop="resourceName" label="素材名称" />
          <el-table-column prop="resourceType" label="素材类型">
            <template #default="scope">
              {{ scope.row.resourceType === 'image' ? '图片' : '视频' }}
            </template>
          </el-table-column>
          <el-table-column prop="resourceUrl" label="素材" width="260">
            <template #default="scope">
              <el-image v-if="scope.row.resourceType === 'image'" :src="scope.row.resourceUrl"
                style="height: 100px"></el-image>
              <video v-else :src="scope.row.resourceUrl" width="200" height="100" controls preload="none"></video>
            </template>
          </el-table-column>
          <el-table-column prop="modifyTime" label="时间" width="250" />
          <el-table-column label="操作">
            <template #default="scope">
              <el-button size="small" type="primary" @click="onEdit(scope.row)">编辑</el-button>
              <el-popconfirm confirm-button-text="确认" cancel-button-text="取消" icon-color="red" title="是否删除该素材?"
                @confirm="handleDelete(scope.row)">
                <!-- @cancel="cancelEvent" -->
                <template #reference>
                  <el-button size="small" type="danger">删除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </el-main>
      <el-footer>
        <el-pagination background layout="prev,pager, next" :total="total" :current-page="getListReq.page"
          @current-change="handlePageChang" />
      </el-footer>
    </el-container>
    <el-dialog v-model="dialogConfig.isShow" title="创建/修改 素材" width="600px">
      <el-form ref="addForm" :model="fromReq" class="demo-form-inline" label-width="100px" :rules="rules">
        <el-form-item label="素材类型" prop="resourceType" @change="onChangeType">
          <el-radio-group v-model="fromReq.resourceType">
            <el-radio-button label="image">图片</el-radio-button>
            <el-radio-button label="video">视频</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="所属功能" prop="useType">
          <el-select v-model="fromReq.useType" class="m-2" placeholder="Select" size="large" style="width: 240px">
            <el-option v-for="item in useType" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="素材名称" prop="resourceName">
          <el-input v-model="fromReq.resourceName" placeholder="名称" />
        </el-form-item>
        <el-form-item label="上传素材" prop="resourceUrl">
          <el-upload ref="uploadImgRef" class="avatar-uploader" :before-upload="beforeAvatarUpload"
            :show-file-list="false" :http-request="upload">
            <div class="upload-img-box" v-if="fromReq.resourceType === 'image'">
              <img v-if="fromReq.resourceUrl" :src="fromReq.resourceUrl" class="preview-img" />
              <div class="upload-btn" v-else>
                <el-icon size="30" color="#cdd0d6"><i-ep-Plus /></el-icon>
              </div>
              <div class="operate-box" v-show="fromReq.resourceUrl">
                <el-icon size="22" color="#ffffff" @click.stop="handleRemove"><i-ep-Delete /></el-icon>
              </div>
            </div>
            <div class="upload-img-box" v-if="fromReq.resourceType === 'video'">
              <!-- <img v-if="fromReq.resourceUrl" :src="fromReq.resourceUrl" class="preview-img" /> -->
              <video :src="fromReq.resourceUrl" v-if="fromReq.resourceUrl" wdith="120" height="120"></video>
              <div class="upload-btn" v-else>
                <el-icon size="30" color="#cdd0d6"><i-ep-Plus /></el-icon>
              </div>
              <div class="operate-box" v-show="fromReq.resourceUrl">
                <el-icon size="22" color="#ffffff" @click.stop="handleRemove"><i-ep-Delete /></el-icon>
              </div>
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit(addForm)">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.mode-box {
  background-color: #eee;
  display: flex;
  padding: 4px 0px;
  border-radius: 6px;
  width: 400px;

  .item {
    background-color: #fff;
    //padding: 0 5px;
    margin: 0 5px;
    flex: 1;
    border-radius: 6px;
    text-align: center;
    cursor: pointer;
    height: 26px;
    line-height: 26px;

    &.active {
      background-color: #409eff;
      color: #fff;
    }
  }
}

.avatar-uploader {
  margin-right: 20px;
}

.upload-img-box {
  width: 120px;
  height: 120px;
  border: 1px solid #cdd0d6;
  box-sizing: border-box;
  position: relative;
  border-radius: 6px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;

  .preview-img {
    width: 100%;
    height: 100%;
  }

  .operate-box {
    position: absolute;
    z-index: 9;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.5);
    visibility: hidden;
  }

  &:hover .operate-box {
    visibility: visible;
  }
}
</style>
