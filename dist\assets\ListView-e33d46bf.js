import{d as re,a5 as de,r as d,a as b,a6 as ie,c as E,b as e,w as t,h as n,A as T,a7 as ue,a8 as me,E as u,e as ce,C as pe,o as f,T as _e,I as x,f as S,i as m,G as z,H as D,U as fe,a9 as ge,aa as ye,ab as be,ac as ve,ad as he,p as we,s as Ve,ae as ke,af as Ie,v as Re,y as Ce,x as Ee,Y as xe,k as Ae,l as Ue,q as Le,m as Ne,n as Ge,Z as Fe,_ as qe}from"./index-6e8b0ade.js";/* empty css                   *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                   *//* empty css                     *//* empty css                      *//* empty css                        *//* empty css                   */const Te={class:"header-box"},Se={class:"dialog-footer"},ze=re({__name:"ListView",setup(De){const B=de(),A=d(0),I=d(!1);let v=b({adminType:"merchant",merchantGuid:"",pageSize:10,page:1}),U=d([]),c=d(!1),h=d("");const L=d(),s=b({guid:"",mobile:"",userName:"",nickname:"",password:"",merchantGuid:"",adminType:"merchant"}),M=b({mobile:[{required:!0,message:"请输入电话",trigger:"blur"},{pattern:/^1\d{10}$/,message:"手机号必须是11位数字",trigger:"blur"}],userName:[{required:!0,message:"请输入用户名",trigger:"blur"}],nickname:[{required:!0,message:"请输入昵称",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"}]}),w=d(!1);let R=ie(()=>B.getTenantInfo.guid);const g=async()=>{I.value=!0,v.merchantGuid=R.value;let l=await ue(v);I.value=!1,A.value=l.data.total,U.value=l.data.data},$=async l=>{l.validate(async a=>{if(a){w.value=!0;try{h.value==="add"?(await ge(s),u.success("新增成功")):h.value==="edit"&&(await ye(s),u.success("修改成功")),c.value=!1,w.value=!1,l.resetFields(),g()}catch(r){throw u.error(r),w.value=!1,new Error(r)}}})},P=async l=>{try{let a=l.guid;await be({guid:a}),g(),u.success("删除成功")}catch(a){u.success(a)}},H=async l=>{v.page=l,g()},O=()=>{s.merchantGuid=R.value,c.value=!0,h.value="add"},Y=l=>{s.guid=l.guid,s.mobile=l.mobile,s.password=l.password,s.userName=l.userName,s.nickname=l.nickname,c.value=!0,h.value="edit"};g();const N=d([]),C=d(),p=d(!1);(async()=>{try{const l=await me({merchantGuid:R.value,pageSize:100,page:1});N.value=l.data.list}catch{u.error("获取角色列表失败")}})();const Z=l=>{i.adminUserId=l.sysId,J(l.sysId)},i=b({merchantGuid:"",adminUserId:0,merchantRoleIds:[]}),j=async()=>{C.value&&await C.value.validate(async l=>{if(l)try{await ve({adminUserId:i.adminUserId,merchantGuid:i.merchantGuid,merchantRoleIds:i.merchantRoleIds}),u.success("设置成功"),p.value=!1,g()}catch(a){u.error(a.message||"设置失败")}})},G=b({merchantGuid:"",adminUserId:""}),J=async l=>{G.adminUserId=l;let a=await he(G);i.merchantRoleIds=a.data.merchantRoleIds,p.value=!0};return(l,a)=>{const r=we,y=Ve,K=ke,Q=Ie,W=Re,X=Ce,ee=Ee,ae=xe,te=ce,V=Ae,_=Ue,F=Le,q=pe,le=Ne,oe=Ge,ne=Fe;return f(),E("div",null,[e(te,{class:"wrapper"},{default:t(()=>[_e((f(),x(X,null,{default:t(()=>[S("div",Te,[e(r,{type:"primary",onClick:O},{default:t(()=>[m("新增管理员")]),_:1})]),e(W,{data:n(U),border:"",style:{width:"100%"}},{default:t(()=>[e(y,{prop:"sysId",label:"Id",width:"80"}),e(y,{prop:"nickname",label:"管理昵称",width:"180"}),e(y,{prop:"userName",label:"账户名称",width:"180"}),e(y,{prop:"roles",label:"角色列表",width:"280"},{default:t(({row:o})=>[(f(!0),E(z,null,D(o.roles,(k,se)=>(f(),x(K,{key:se,size:"small",type:"success",style:{"margin-right":"3px"}},{default:t(()=>[m(fe(k.roleName),1)]),_:2},1024))),128))]),_:1}),e(y,{label:"操作"},{default:t(o=>[e(r,{size:"small",type:"primary",onClick:k=>Y(o.row)},{default:t(()=>[m("编辑")]),_:2},1032,["onClick"]),e(r,{size:"small",type:"warning",onClick:k=>Z(o.row)},{default:t(()=>[m("绑定角色")]),_:2},1032,["onClick"]),e(Q,{"confirm-button-text":"确认","cancel-button-text":"取消","icon-color":"red",title:"是否删除该管理?",onConfirm:k=>P(o.row)},{reference:t(()=>[e(r,{size:"small",type:"danger"},{default:t(()=>[m("删除")]),_:1})]),_:2},1032,["onConfirm"])]),_:1})]),_:1},8,["data"])]),_:1})),[[ne,n(I)]]),e(ae,null,{default:t(()=>[e(ee,{background:"",layout:"prev,pager, next",total:n(A),"current-page":n(v).page,onCurrentChange:H},null,8,["total","current-page"])]),_:1})]),_:1}),e(q,{modelValue:n(c),"onUpdate:modelValue":a[5]||(a[5]=o=>T(c)?c.value=o:c=o),title:"新增/修改 管理员",width:"600px"},{default:t(()=>[e(F,{ref_key:"addForm",ref:L,model:n(s),rules:n(M)},{default:t(()=>[e(_,{label:"电话",prop:"mobile"},{default:t(()=>[e(V,{modelValue:n(s).mobile,"onUpdate:modelValue":a[0]||(a[0]=o=>n(s).mobile=o),autocomplete:"off",placeholder:"电话"},null,8,["modelValue"])]),_:1}),e(_,{label:"昵称",prop:"nickname"},{default:t(()=>[e(V,{modelValue:n(s).nickname,"onUpdate:modelValue":a[1]||(a[1]=o=>n(s).nickname=o),autocomplete:"off",placeholder:"昵称"},null,8,["modelValue"])]),_:1}),e(_,{label:"用户",prop:"userName"},{default:t(()=>[e(V,{modelValue:n(s).userName,"onUpdate:modelValue":a[2]||(a[2]=o=>n(s).userName=o),autocomplete:"off",placeholder:"用户"},null,8,["modelValue"])]),_:1}),e(_,{label:"密码",prop:"password"},{default:t(()=>[e(V,{type:"password",modelValue:n(s).password,"onUpdate:modelValue":a[3]||(a[3]=o=>n(s).password=o),autocomplete:"off",placeholder:"密码"},null,8,["modelValue"])]),_:1}),e(_,null,{default:t(()=>[e(r,{type:"primary",onClick:a[4]||(a[4]=o=>$(n(L))),loading:n(w)},{default:t(()=>[m("提交")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"]),e(q,{title:"权限设置",modelValue:n(p),"onUpdate:modelValue":a[8]||(a[8]=o=>T(p)?p.value=o:null),width:"700px"},{footer:t(()=>[S("span",Se,[e(r,{onClick:a[7]||(a[7]=o=>p.value=!1)},{default:t(()=>[m("取消")]),_:1}),e(r,{type:"primary",onClick:j},{default:t(()=>[m("确定")]),_:1})])]),default:t(()=>[e(F,{ref_key:"formRef",ref:C,model:n(i),"label-width":"120px"},{default:t(()=>[e(_,{label:"角色选择",prop:"merchantRoleIds"},{default:t(()=>[e(oe,{modelValue:n(i).merchantRoleIds,"onUpdate:modelValue":a[6]||(a[6]=o=>n(i).merchantRoleIds=o),multiple:"",placeholder:"请选择角色",style:{width:"100%"}},{default:t(()=>[(f(!0),E(z,null,D(n(N),o=>(f(),x(le,{key:o.sysId,label:o.roleName,value:o.sysId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}});const We=qe(ze,[["__scopeId","data-v-49864b87"]]);export{We as default};
