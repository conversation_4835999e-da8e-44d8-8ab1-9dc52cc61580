<template>
  <div>
    <el-container class="wrapper">
      <el-main v-loading="loading">
        <el-table :data="goodsList" border style="width: 100%">
          <el-table-column prop="user.sysId" label="用户Id" />
          <el-table-column prop="user.nickname" label="用户昵称" width="180" />
          <el-table-column prop="user.mobile" label="手机号" width="120" />
          <el-table-column prop="withdrawOrderNo" :show-overflow-tooltip="true" label="提现单号" width="120" />
          <el-table-column prop="payVoucherImg" label="打款凭据">
            <template #default="scope">
              <el-image :src="scope.row.payVoucherImg" :initial-index="0" :preview-teleported="true"
                :preview-src-list="scope.row.imageResult"
                style="width: 50px; height: 50px; border-radius: 50%"></el-image>
            </template>
          </el-table-column>
          <el-table-column prop="bigVipGuid" label="提现信息">
            <template #default="scope">
              <span v-if="scope.row.userWithdraMethod.withdrawMethod == 1">提现方式：微信</span>
              <span v-if="scope.row.userWithdraMethod.withdrawMethod == 2">提现方式：支付宝</span>
              <span v-if="scope.row.userWithdraMethod.withdrawMethod == 3">提现方式：银行卡</span>
              <p style="margin: 0px">提现账号: {{ scope.row.userWithdraMethod.withdrawAccount }}</p>
              <p style="margin: 0px">收款人: {{ scope.row.userWithdraMethod.withdrawName }}</p>
              <p style="margin: 0px" v-if="scope.row.userWithdraMethod.withdrawMethod == 3">
                银行: {{ scope.row.userWithdraMethod.bankName }}
              </p>
              <p style="margin: 0px" v-if="scope.row.userWithdraMethod.withdrawMethod == 3">
                支行: {{ scope.row.userWithdraMethod.bankBranchName }}
              </p>
            </template>
          </el-table-column>
          <el-table-column prop="withdrawAmount" label="提现金额" />
          <el-table-column prop="withdrawStatusText" label="提现状态" />
          <el-table-column prop="withdrawTime" label="申请时间" />
          <el-table-column label="操作">
            <template #default="scope">
              <el-button size="small" type="primary" @click="onShowApprove(scope.row)">审批</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-main>
      <el-footer>
        <el-pagination background layout="prev,pager, next" :total="total" :current-page="reqForm.page"
          @current-change="handlePageChang" />
      </el-footer>
    </el-container>
    <el-dialog v-model="approveVisible" title="审核信息" width="500">
      <el-form ref="appForm" :model="applyForm" :rules="rules">
        <el-form-item label="审批状态" prop="withdrawStatus" label-width="80px">
          <el-select v-model="applyForm.withdrawStatus">
            <el-option :label="item.name" :value="item.type" v-for="item in modeList" />
          </el-select>
        </el-form-item>
        <el-form-item label="打款凭据" prop="payVoucherImg" label-width="80px">
          <el-upload ref="uploadImgRef" class="avatar-uploader" :before-upload="beforeAvatarUpload"
            :show-file-list="false" :http-request="upload">
            <div class="upload-img-box">
              <img v-if="applyForm.payVoucherImg" :src="applyForm.payVoucherImg" class="preview-img" />
              <div class="upload-btn" v-else>
                <el-icon size="30" color="#cdd0d6"><i-ep-Plus /></el-icon>
              </div>
              <div class="operate-box" v-show="applyForm.payVoucherImg" @click.stop="handleRemove">
                <el-icon size="22" color="#ffffff"><i-ep-Delete /></el-icon>
              </div>
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="approveVisible = false">取 消</el-button>
          <el-button type="primary" @click="onApprove(appForm)"> 确 认 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { userWithdrawRecords, approveWithdraw, uploadImg } from '@/api';
import type { FormInstance, UploadProps, FormRules } from 'element-plus';
import { useAdminCommonStore } from '@/stores/adminCommon';
const store = useAdminCommonStore();
//分页
const total = ref(0);
const loading = ref(false);
const modeList = reactive([
  {
    id: 1,
    type: '200',
    name: '审核通过',
  },
  {
    id: 2,
    type: '300',
    name: '审核拒绝',
  },
]);
const reqForm = reactive({
  merchantGuid: '',
  // applyStatus: '100',
  pageSize: 10,
  page: 1,
});
let merchantGuid = computed(() => {
  return store.getTenantInfo.guid;
});
let goodsList = ref([]);
//获取申请列表
const getList = async () => {
  loading.value = true;
  reqForm.merchantGuid = merchantGuid.value;
  let goodsRes = await userWithdrawRecords(reqForm);
  loading.value = false;
  total.value = goodsRes.data.total;
  goodsRes.data.data.forEach((item) => {
    item.imageResult = [item.payVoucherImg];
  });
  goodsList.value = goodsRes.data.data;
};
const handlePageChang = async (page) => {
  reqForm.page = page;
  getList();
};
const appForm = ref<FormInstance>();
const applyForm = reactive({
  guid: '',
  withdrawStatus: '200',
  payVoucherImg: '',
});
const rules = reactive<FormRules>({
  withdrawStatus: [{ required: true, message: '请选择状态', trigger: 'change' }],
  payVoucherImg: [{ required: true, message: '请上传凭据', trigger: 'change' }],
});
const beforeAvatarUpload: UploadProps['beforeUpload'] = (rawFile) => {
  let fileTypes = ['image/jpg', 'image/png', 'image/jpeg'];
  if (!fileTypes.includes(rawFile.type)) {
    ElMessage.warning('当前图片仅支持格式为：' + fileTypes.join(' ，'));
    return false;
  }
};
const upload = async (file) => {
  const formData = new FormData();
  // 添加要上传的文件名和文件对象,key就是你上传文件的字段名
  formData.append('img', file.file);
  let res = await uploadImg(formData);
  applyForm.payVoucherImg = res.data;
};
const handleRemove = () => {
  applyForm.payVoucherImg = '';
};
const approveVisible = ref(false);
const onShowApprove = (item) => {
  applyForm.guid = item.guid;
  approveVisible.value = true;
};
const onApprove = async (formEl) => {
  formEl.validate(async (valid) => {
    if (!valid) {
      return;
    }
    try {
      await approveWithdraw(applyForm);
      ElMessage({
        message: '审核通过',
        type: 'success',
      });
      approveVisible.value = false;
      getList();
    } catch (error) { }
  });
};
//搜索
const onSearch = () => {
  getList();
};
getList();
</script>
<style scoped lang="scss">
.upload-img-box {
  width: 120px;
  height: 120px;
  border: 1px solid #cdd0d6;
  box-sizing: border-box;
  position: relative;
  border-radius: 6px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;

  .preview-img {
    width: 100%;
    height: 100%;
  }

  .operate-box {
    position: absolute;
    z-index: 9;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.5);
    visibility: hidden;
  }

  &:hover .operate-box {
    visibility: visible;
  }
}
</style>
