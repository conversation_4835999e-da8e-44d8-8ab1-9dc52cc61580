<script setup lang="ts">
import { getMerchantDetailApi } from '@/api';
import { useAdminCommonStore } from '@/stores/adminCommon';
import screenfull from 'screenfull';
import { useRouter } from 'vue-router';

const router = useRouter();
const store = useAdminCommonStore();
const isCollapse = computed(() => {
  return store.isCollapse;
});

let tenantInfo = reactive({
  merchantName: '一城一智',
});
let adminName = computed(() => {
  return store.getAdminInfo.userName;
});
//合并菜单
const handleChangeColl = () => {
  store.changeSideWidth();
};
//刷新
const handleRefresh = () => {
  location.reload();
};
const toggle = () => {
  if (!screenfull.isEnabled) {
    return false;
  }
  // isFullscreen.value = !isFullscreen.value;
  screenfull.toggle();
};
const handelLogout = () => {
  store.outLogin();
  router.push('/login');
};
const getMerchantDetail = async () => {
  let res = await getMerchantDetailApi();
  tenantInfo.merchantName = res.data.merchantName;
  store.saveTenantInfo(res.data);
};
getMerchantDetail();
</script>

<template>
  <div class="f-header">
    <div class="left-box">
      <div class="logo-box">{{ tenantInfo.merchantName }}</div>
      <el-tooltip effect="dark" :content="isCollapse ? '展开菜单' : '合并菜单'" placement="bottom">
        <el-icon class="ico" @click="handleChangeColl">
          <i-ep-Expand v-if="isCollapse" />
          <i-ep-Fold v-else />
        </el-icon>
      </el-tooltip>
      <el-tooltip effect="dark" content="刷新" placement="bottom">
        <el-icon class="ico" @click="handleRefresh">
          <i-ep-Refresh />
        </el-icon>
      </el-tooltip>
    </div>
    <div class="right-box">
      <!-- isFullscreen ? '关闭全屏' :  -->
      <el-tooltip effect="dark" content="全屏" placement="bottom">
        <el-icon class="ico" @click="toggle">
          <i-ep-FullScreen />
          <!-- <i-ep-Rank v-else />v-if="!isFullscreen" -->
        </el-icon>
      </el-tooltip>
      <el-dropdown>
        <span class="el-dropdown-link user-info">
          <!-- <img class="logo-img" :src="info.avatar" /> -->
          <span class="user-name">{{ adminName }}</span>
          <el-icon>
            <i-ep-arrow-down />
          </el-icon>
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <!-- <el-dropdown-item @click="drawer = true">修改密码</el-dropdown-item> -->
            <el-dropdown-item @click="handelLogout">退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
  <!-- <el-drawer v-model="drawer" :direction="direction" title="修改密码">
    <template #default>
      <el-form ref="editFrom" :model="editData" class="w-[250px]" :rules="rules">
        <el-form-item prop="oldPassword">
          <el-input type="password" autocomplete="off" v-model="editData.oldPassword" placeholder="请输入密码"></el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input type="password" autocomplete="off" v-model="editData.password" placeholder="请输入新密码"></el-input>
        </el-form-item>
        <el-form-item prop="repassword">
          <el-input type="password" autocomplete="off" v-model="editData.repassword" placeholder="请确认输入密码"></el-input>
        </el-form-item>
      </el-form>
    </template>
    <template #footer>
      <div class="text-left">
        <el-button type="primary" :loading="logiLoading" @click="submitForm(editFrom)">提交</el-button>
        <el-button @click="resetForm(editFrom)">重置</el-button>
      </div>
    </template>
  </el-drawer> -->
</template>

<style scoped lang="scss">
.f-header {
  background-color: rgba(67, 56, 202, 1);
  display: flex;
  align-items: center;
  position: fixed;
  justify-content: space-between;
  top: 0px;
  left: 0px;
  right: 0px;
  box-shadow: 0px 1px 1px 0 rgb(0, 0, 0, 0.2);
  color: rgba(253, 253, 253, 1);
  z-index: 60;
  height: 40px;
}
.left-box {
  height: 100%;
  display: flex;
  align-items: center;
  .logo-box {
    height: 100%;
    font-size: 36rpx;
    color: #fff;
    font-weight: bold;
    width: 250px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .ico {
    cursor: pointer;
    height: 100%;
    width: 42px;
    &:hover {
      background-color: rgba(79, 70, 229, 1);
    }
  }
}
.right-box {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  .el-dropdown-link:focus {
    outline: none;
  }
  .user-info {
    height: 100%;
    display: flex;
    align-items: center;
    color: #fff;
    padding: 0 20px;
    cursor: pointer;
    border: none;
    .logo-img {
      width: 25px;
      height: 25px;
      border-radius: 50%;
      margin-right: 20px;
    }
    .user-name {
      margin-right: 10px;
    }
  }
  .ico {
    cursor: pointer;
    height: 100%;
    width: 42px;
    &:hover {
      background-color: rgba(79, 70, 229, 1);
    }
  }
}
</style>
