import{d as J,r as _,a as B,c as k,b as e,w as t,h as s,A as C,D as P,b3 as Q,e as W,C as X,o as u,T as Y,I as f,f as d,U as c,G as E,H as z,i as n,k as F,b4 as ee,ax as te,b5 as le,s as oe,ae as se,p as ae,v as ne,y as de,b6 as ie,t as ue,l as re,q as _e,Z as ce,_ as me}from"./index-6e8b0ade.js";/* empty css                   *//* empty css                     *//* empty css                  *//* empty css                   *//* empty css                  *//* empty css                *//* empty css                        *//* empty css               */const pe={class:"content-text"},fe={class:"text-paragraph-box"},we={class:"content"},ve={class:"keywords"},ye={class:"title"},be={class:"item-box"},ke={class:"detail"},ge={class:"text-paragraph-box"},he={class:"keywords"},xe={class:"title"},Ve={class:"item-box"},Ce=J({__name:"KnowledgeFileDetailView",setup(Ee){const I=P().query.fileGuid,g=_(!1),y=_(!1),w=_(!1);let D=_([]);const S=async()=>{g.value=!0;let i=await Q({fileGuid:I});g.value=!1,D.value=i.data.data};S();let o=B({word_count:0,enabled:!1,content:"",keywords:[],index_node_hash:"",hit_count:0,id:""});const R=i=>{o={fileItem:o,...i},y.value=!0},$=i=>{o=B(Object.assign({},o,i)),w.value=!0},m=_(""),h=_(!1),K=_(),q=i=>{o.keywords.splice(o.keywords.indexOf(i),1)},A=()=>{h.value=!0,te(()=>{K.value.input.focus()})},T=()=>{m.value&&o.keywords.push(m.value),h.value=!1,m.value=""},G=_(),L=async()=>{let i={fileGuid:I,segmentId:o.id,segmentsData:{segment:o}},a=await le(i);w.value=!1,S(),console.log(a,"resresresres")};return(i,a)=>{const v=oe,x=se,b=ae,N=ne,O=de,j=W,p=ie,H=ue,U=X,V=re,M=_e,Z=ce;return u(),k("div",null,[e(j,{class:"wrapper"},{default:t(()=>[e(O,null,{default:t(()=>[Y((u(),f(N,{data:s(D),border:"",style:{width:"100%"}},{default:t(()=>[e(v,{prop:"tokens",label:"tokens",width:"80"}),e(v,{prop:"word_count",label:"字数",width:"80"}),e(v,{label:"文档段落"},{default:t(l=>[d("div",pe,c(l.row.content),1)]),_:1}),e(v,{label:"关键字",width:"300"},{default:t(l=>[(u(!0),k(E,null,z(l.row.keywords,r=>(u(),f(x,{type:"info",key:r},{default:t(()=>[n(c(r),1)]),_:2},1024))),128))]),_:1}),e(v,{label:"操作",width:"200"},{default:t(l=>[e(b,{size:"small",type:"primary",onClick:r=>R(l.row)},{default:t(()=>[n("查看")]),_:2},1032,["onClick"]),e(b,{size:"small",type:"primary",onClick:r=>$(l.row)},{default:t(()=>[n("编辑")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Z,s(g)]])]),_:1})]),_:1}),e(U,{modelValue:s(y),"onUpdate:modelValue":a[2]||(a[2]=l=>C(y)?y.value=l:null),title:"文档段落",width:"600"},{default:t(()=>[d("div",fe,[d("div",we,[e(s(F),{"input-style":{width:"100%"},modelValue:s(o).content,"onUpdate:modelValue":a[0]||(a[0]=l=>s(o).content=l),rows:20,disabled:"",type:"textarea",resize:"none"},null,8,["modelValue"])]),d("div",ve,[d("div",ye,[e(p,{size:"small"},{default:t(()=>[n("关键词")]),_:1})]),d("div",be,[(u(!0),k(E,null,z(s(o).keywords,(l,r)=>(u(),f(x,{key:r},{default:t(()=>[n(c(l),1)]),_:2},1024))),128))])]),d("div",ke,[e(p,{class:"mx-1",size:"small"},{default:t(()=>[n(c(s(o).word_count)+"字符",1)]),_:1}),e(p,{class:"mx-1",size:"small"},{default:t(()=>[n(c(s(o).hit_count)+"召回次数",1)]),_:1}),e(p,{class:"mx-3",size:"small",truncated:""},{default:t(()=>[n("向量哈希:"+c(s(o).index_node_hash),1)]),_:1}),d("div",null,[e(p,{class:"status",size:"small"},{default:t(()=>[n(c(s(o).enabled?"已启用":"未启用"),1)]),_:1}),e(H,{modelValue:s(o).enabled,"onUpdate:modelValue":a[1]||(a[1]=l=>s(o).enabled=l),disabled:""},null,8,["modelValue"])])])])]),_:1},8,["modelValue"]),e(U,{modelValue:s(w),"onUpdate:modelValue":a[6]||(a[6]=l=>C(w)?w.value=l:null),title:"编辑段落",width:"600"},{default:t(()=>[d("div",ge,[e(M,{ref_key:"addForm",ref:G,model:s(o),class:"demo-form-inline","label-width":"0px"},{default:t(()=>[e(V,{prop:"content"},{default:t(()=>[e(s(F),{"input-style":{width:"100%"},modelValue:s(o).content,"onUpdate:modelValue":a[3]||(a[3]=l=>s(o).content=l),rows:20,type:"textarea",resize:"none"},null,8,["modelValue"])]),_:1}),e(V,{prop:"keywords"},{default:t(()=>[d("div",he,[d("div",xe,[e(p,{size:"small"},{default:t(()=>[n("关键词")]),_:1})]),d("div",Ve,[(u(!0),k(E,null,z(s(o).keywords,(l,r)=>(u(),f(x,{key:r,closable:"","disable-transitions":!1,onClose:Fe=>q(l)},{default:t(()=>[n(c(l),1)]),_:2},1032,["onClose"]))),128)),s(h)?(u(),f(s(F),{key:0,ref_key:"InputRef",ref:K,modelValue:s(m),"onUpdate:modelValue":a[4]||(a[4]=l=>C(m)?m.value=l:null),class:"w-20",size:"small",onKeyup:ee(T,["enter"]),onBlur:T},null,8,["modelValue","onKeyup"])):(u(),f(b,{key:1,class:"button-new-tag",size:"small",onClick:A},{default:t(()=>[n(" + 新增 ")]),_:1}))])])]),_:1}),e(V,null,{default:t(()=>[e(b,{type:"primary",onClick:a[5]||(a[5]=l=>L())},{default:t(()=>[n("保存")]),_:1})]),_:1})]),_:1},8,["model"])])]),_:1},8,["modelValue"])])}}});const qe=me(Ce,[["__scopeId","data-v-ad30db29"]]);export{qe as default};
