<script setup lang="ts">

import {
  getAiAgentListApi,
  getAiAgentCategoryApi,
  auditAiAgentApi,
  updateAiAgentStatusApi,
  updateAiAgentInfoApi
} from '@/api';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';

// 分页参数
const total = ref(0);
const loading = ref(false);

// 搜索筛选参数
const searchParams = reactive({
  merchantGuid: '',
  categoryGuid: '',
  agentType: '',
  auditStatus: '',
  status: '',
  isPaid: '',
  agentName: '',
  creatorNickname: '',
  pageSize: 10,
  page: 1,
});

// 数据类型定义
interface AgentItem {
  categoryGuid: string;
  agentType: number;
  auditStatus: number;
  status: number;
  isPaid: number;
  agentName: string;
  creatorNickname: string;
  [key: string]: any;
}

// 列表数据
let agentList = ref<AgentItem[]>([]);
let categoryList: any = ref([]);

// 审核弹窗相关
const auditDialogVisible = ref(false);
const auditFormRef = ref<FormInstance>();
const currentAuditItem = ref<AgentItem | null>(null);

// 审核表单数据
const auditFormData = reactive({
  guid: '',
  auditStatus: 2, // 默认审核通过
  auditRemark: '',
});

// 审核表单验证规则
const auditRules: FormRules = {
  auditStatus: [{ required: true, message: '请选择审核状态', trigger: 'change' }],
  auditRemark: [
    {
      validator: (rule, value, callback) => {
        if (auditFormData.auditStatus === 3 && !value) {
          callback(new Error('审核拒绝时必须填写拒绝原因'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ]
};

// 编辑弹窗相关
const editDialogVisible = ref(false);
const editFormRef = ref<FormInstance>();
const currentEditItem = ref<AgentItem | null>(null);

// 编辑表单数据
const editFormData = reactive({
  guid: '',
  price: 0, // 新增价格字段，默认值为数字
  isFeatured: 0, // 新增是否为精品字段
});

// 获取智能体列表
const getList = async () => {
  loading.value = true;
  try {
    let res = await getAiAgentListApi(searchParams);
    if (res.data && Array.isArray(res.data)) {
      // 直接数组格式
      agentList.value = res.data;
      total.value = res.data.length;
    } else if (res.data && res.data.data) {
      // 分页格式
      agentList.value = res.data.data;
      total.value = res.data.total;
    }
  } catch (error) {
    ElMessage.error('获取智能体列表失败');
  } finally {
    loading.value = false;
  }
};

// 获取分类列表
const getCategoryList = async () => {
  try {
    let res = await getAiAgentCategoryApi({ merchantGuid: searchParams.merchantGuid });
    if (res.data && Array.isArray(res.data)) {
      categoryList.value = res.data;
    } else if (res.data && res.data.data) {
      categoryList.value = res.data.data;
    }
    getList();
  } catch (error) {
    ElMessage.error('获取分类列表失败');
  }
};

// 搜索功能
const handleSearch = () => {
  searchParams.page = 1;
  getList();
};

// 重置搜索
const handleReset = () => {
  searchParams.categoryGuid = '';
  searchParams.agentType = '';
  searchParams.auditStatus = '';
  searchParams.status = '';
  searchParams.isPaid = '';
  searchParams.agentName = '';
  searchParams.creatorNickname = '';
  searchParams.page = 1;
  getList();
};

// 分页切换
const handlePageChange = (page: number) => {
  searchParams.page = page;
  getList();
};

// 每页条数切换
const handleSizeChange = (size: number) => {
  searchParams.pageSize = size;
  searchParams.page = 1;
  getList();
};


// 打开审核弹窗
const openAuditDialog = (item: AgentItem) => {
  currentAuditItem.value = item;
  auditFormData.guid = item.guid || '';
  auditFormData.auditStatus = item.auditStatus; // 初始化为当前审核状态
  auditFormData.auditRemark = '';
  auditDialogVisible.value = true;
};

// 提交审核
const handleAuditSubmit = async () => {
  if (!auditFormRef.value) return;

  await auditFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await auditAiAgentApi({
          guid: auditFormData.guid,
          auditStatus: auditFormData.auditStatus,
          auditRemark: auditFormData.auditRemark,
        });

        ElMessage.success('审核操作成功');
        auditDialogVisible.value = false;
        getList(); // 刷新列表
      } catch (error) {
        ElMessage.error('操作失败');
      }
    }
  });
};

// 打开编辑弹窗
const openEditDialog = (item: AgentItem) => {
  currentEditItem.value = item;
  editFormData.guid = item.guid || '';
  editFormData.price = Number(item.price || 0); // 确保 price 为数字类型
  editFormData.isFeatured = item.isFeatured || 0; // 初始化是否为精品
  editDialogVisible.value = true;
};

// 提交编辑
const handleEditSubmit = async () => {
  if (!editFormRef.value) return;

  await editFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 调用更新智能体信息接口
        await updateAiAgentInfoApi({
          guid: editFormData.guid,
          price: Number(editFormData.price), // 确保传递的 price 为数字类型
          isFeatured: editFormData.isFeatured,
        });

        ElMessage.success('编辑成功');
        editDialogVisible.value = false;
        getList(); // 刷新列表
      } catch (error) {
        ElMessage.error('编辑失败');
      }
    }
  });
};

// 状态修改
const handleStatusChange = async (item: AgentItem) => {
  try {
    // 切换状态：1-启用变为2-禁用，2-禁用变为1-启用
    const newStatus = item.status === 1 ? 2 : 1;
    await updateAiAgentStatusApi({
      guid: item.guid,
      status: newStatus
    });

    ElMessage.success(`${newStatus === 1 ? '启用' : '禁用'}成功`);
    getList(); // 刷新列表
  } catch (error) {
    ElMessage.error('状态修改失败');
  }
};

// 辅助函数 - 智能体类型
const getAgentTypeText = (type: number) => {
  const typeMap = {
    1: '内部',
    2: 'dify',
    3: 'coze',
    4: '阿里云百炼'
  };
  return typeMap[type] || '未知';
};

const getAgentTypeTag = (type: number) => {
  const tagMap = {
    1: '',  // 改为空字符串，使用默认样式
    2: 'success',
    3: 'warning',
    4: 'info'
  };
  return tagMap[type] || '';
};

// 辅助函数 - 审核状态
const getAuditStatusText = (status: number) => {
  const statusMap = {
    1: '待审核',
    2: '审核通过',
    3: '审核拒绝'
  };
  return statusMap[status] || '未知';
};

const getAuditStatusTag = (status: number) => {
  const tagMap = {
    1: 'warning',
    2: 'success',
    3: 'danger'
  };
  return tagMap[status] || '';
};

// 初始化
getCategoryList();
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main>
        <!-- 搜索筛选区域 -->
        <div class="search-box">
          <el-form :inline="true" class="search-form">
            <el-form-item label="分类">
              <el-select v-model="searchParams.categoryGuid" placeholder="请选择分类" clearable style="width: 200px">
                <el-option :label="item.categoryName" :value="item.guid" v-for="item in categoryList"
                  :key="item.guid" />
              </el-select>
            </el-form-item>
            <el-form-item label="智能体类型">
              <el-select v-model="searchParams.agentType" placeholder="请选择类型" clearable style="width: 150px">
                <el-option label="内部" :value="1" />
                <el-option label="dify" :value="2" />
                <el-option label="coze" :value="3" />
                <el-option label="阿里云百炼" :value="4" />
              </el-select>
            </el-form-item>
            <el-form-item label="审核状态">
              <el-select v-model="searchParams.auditStatus" placeholder="请选择审核状态" clearable style="width: 150px">
                <el-option label="待审核" :value="1" />
                <el-option label="审核通过" :value="2" />
                <el-option label="审核拒绝" :value="3" />
              </el-select>
            </el-form-item>
            <el-form-item label="状态">
              <el-select v-model="searchParams.status" placeholder="请选择状态" clearable style="width: 150px">
                <el-option label="启用" :value="1" />
                <el-option label="禁用" :value="2" />
              </el-select>
            </el-form-item>
            <el-form-item label="是否付费">
              <el-select v-model="searchParams.isPaid" placeholder="请选择" clearable style="width: 150px">
                <el-option label="免费" :value="0" />
                <el-option label="付费" :value="1" />
              </el-select>
            </el-form-item>
            <el-form-item label="智能体名称">
              <el-input v-model="searchParams.agentName" placeholder="请输入智能体名称" clearable style="width: 200px" />
            </el-form-item>
            <el-form-item label="创作者昵称">
              <el-input v-model="searchParams.creatorNickname" placeholder="请输入创作者昵称" clearable style="width: 200px" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 表格区域 -->
        <el-table :data="agentList" border style="width: 100%" v-loading="loading">
          <el-table-column prop="categoryGuid" label="分类GUID" width="320" />
          <el-table-column prop="agentType" label="智能体类型" width="120">
            <template #default="scope">
              <el-tag :type="getAgentTypeTag(scope.row.agentType)">
                {{ getAgentTypeText(scope.row.agentType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="auditStatus" label="审核状态" width="100">
            <template #default="scope">
              <el-tag :type="getAuditStatusTag(scope.row.auditStatus)">
                {{ getAuditStatusText(scope.row.auditStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="80">
            <template #default="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                {{ scope.row.status === 1 ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="isPaid" label="是否付费" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.isPaid === 1 ? 'warning' : 'success'">
                {{ scope.row.isPaid === 1 ? '付费' : '免费' }}
              </el-tag>
            </template>
          </el-table-column>
          <!-- 新增是否为精品列 -->
          <el-table-column prop="isFeatured" label="是否为精品" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.isFeatured === 1 ? 'success' : 'info'">
                {{ scope.row.isFeatured === 1 ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="priceText" label="价格" />
          <el-table-column prop="agentName" label="智能体名称" show-overflow-tooltip />
          <el-table-column prop="creator.nickname" label="创作者昵称" show-overflow-tooltip />
          <el-table-column fixed="right" label="操作" min-width="180">
            <template #default="scope">
              <el-button size="small" type="primary" @click="openEditDialog(scope.row)">
                编辑
              </el-button>
              <el-button size="small" type="primary" @click="openAuditDialog(scope.row)"
                :disabled="scope.row.auditStatus === 2">
                审核
              </el-button>
              <el-button size="small" :type="scope.row.status === 1 ? 'danger' : 'success'"
                @click="handleStatusChange(scope.row)">
                {{ scope.row.status === 1 ? '禁用' : '启用' }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <div class="pagination-container">
          <el-pagination v-model:current-page="searchParams.page" v-model:page-size="searchParams.pageSize"
            :page-sizes="[10, 20, 50, 100]" :total="total" layout="total, sizes, prev, pager, next, jumper"
            :prev-text="'上一页'" :next-text="'下一页'" @size-change="handleSizeChange" @current-change="handlePageChange" />
        </div>
      </el-main>
    </el-container>

    <!-- 审核弹窗 -->
    <el-dialog title="智能体审核" v-model="auditDialogVisible" width="600px">
      <el-form ref="auditFormRef" :model="auditFormData" :rules="auditRules" label-width="100px">
        <el-form-item label="智能体信息">
          <div v-if="currentAuditItem">
            <div><strong>名称：</strong>{{ currentAuditItem.agentName }}</div>
            <div><strong>创作者：</strong>{{ currentAuditItem.creator.nickname }}</div>
            <div><strong>当前状态：</strong>
              <el-tag :type="getAuditStatusTag(currentAuditItem.auditStatus)">
                {{ getAuditStatusText(currentAuditItem.auditStatus) }}
              </el-tag>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="审核状态" prop="auditStatus">
          <el-radio-group v-model="auditFormData.auditStatus">
            <el-radio :label="2">审核通过</el-radio>
            <el-radio :label="3">审核拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="拒绝原因" prop="auditRemark" v-if="auditFormData.auditStatus === 3">
          <el-input v-model="auditFormData.auditRemark" type="textarea" :rows="4" placeholder="请输入拒绝原因" maxlength="200"
            show-word-limit />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="auditDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleAuditSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 编辑弹窗 -->
    <el-dialog title="智能体编辑" v-model="editDialogVisible" width="600px">
      <el-form ref="editFormRef" :model="editFormData" label-width="100px">
        <el-form-item label="智能体信息">
          <div v-if="currentEditItem">
            <div><strong>名称：</strong>{{ currentEditItem.agentName }}</div>
            <div><strong>创作者：</strong>{{ currentEditItem.creator.nickname }}</div>
          </div>
        </el-form-item>
        <el-form-item label="价格">
          <el-input-number v-model="editFormData.price" :min="0" style="width: 100%" />
        </el-form-item>
        <el-form-item label="是否为精品">
          <el-switch v-model="editFormData.isFeatured" :active-value="1" :inactive-value="0" active-text="是"
            inactive-text="否" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleEditSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.search-box {
  background: #f5f7fa;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.search-form {
  margin: 0;
}

.search-form .el-form-item {
  margin-bottom: 10px;
}

.pagination-container {
  display: flex;
  justify-content: flex-start;
  margin-top: 20px;
  padding: 20px 0;
}
</style>