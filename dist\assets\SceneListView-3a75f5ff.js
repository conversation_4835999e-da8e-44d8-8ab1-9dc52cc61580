import{o as d,c,f as v,d as he,a as V,r,b as e,w as l,h as a,A as ke,aA as xe,aB as Le,e as Ce,C as Se,au as Ee,i as p,G as x,H as L,aC as Ue,U as A,T as J,I as f,F as R,ah as ze,ai as Ae,E as b,J as Re,aD as Ie,aE as Me,ax as Fe,aF as qe,aG as Q,aH as Be,p as De,l as Ge,q as Te,s as $e,N as Ne,t as je,v as He,y as Pe,aI as Oe,am as Ke,m as Je,n as Qe,k as Ze,O as We,P as Xe,a3 as Ye,al as et,Z as tt,az as lt,_ as at}from"./index-6e8b0ade.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                 *//* empty css                        *//* empty css                 *//* empty css                  *//* empty css                    */import{_ as ot,a as nt}from"./plus-19e9063c.js";/* empty css               *//* empty css                  *//* empty css                       *//* empty css                        *//* empty css                        *//* empty css                  *//* empty css                        *//* empty css                     */const st={viewBox:"0 0 1024 1024",width:"1.2em",height:"1.2em"},it=v("path",{fill:"currentColor",d:"M512 64a32 32 0 0 1 32 32v192a32 32 0 0 1-64 0V96a32 32 0 0 1 32-32zm0 640a32 32 0 0 1 32 32v192a32 32 0 1 1-64 0V736a32 32 0 0 1 32-32zm448-192a32 32 0 0 1-32 32H736a32 32 0 1 1 0-64h192a32 32 0 0 1 32 32zm-640 0a32 32 0 0 1-32 32H96a32 32 0 0 1 0-64h192a32 32 0 0 1 32 32zM195.2 195.2a32 32 0 0 1 45.248 0L376.32 331.008a32 32 0 0 1-45.248 45.248L195.2 240.448a32 32 0 0 1 0-45.248zm452.544 452.544a32 32 0 0 1 45.248 0L828.8 783.552a32 32 0 0 1-45.248 45.248L647.744 692.992a32 32 0 0 1 0-45.248zM828.8 195.264a32 32 0 0 1 0 45.184L692.992 376.32a32 32 0 0 1-45.248-45.248l135.808-135.808a32 32 0 0 1 45.248 0zm-452.544 452.48a32 32 0 0 1 0 45.248L240.448 828.8a32 32 0 0 1-45.248-45.248l135.808-135.808a32 32 0 0 1 45.248 0z"},null,-1),dt=[it];function ut(Z,I){return d(),c("svg",st,dt)}const rt={name:"ep-loading",render:ut},pt={class:"header-box"},_t={class:"mode-box"},ct=["onClick"],mt={class:"upload-img-box"},gt=["src"],ft={key:1,class:"upload-btn"},vt={class:"operate-box"},wt={class:"qrcode-pop-box"},bt={class:"image-slot"},yt=he({__name:"SceneListView",setup(Z){const I=V({merchantGuid:"",pageSize:100,page:1}),M=r([]),F=async()=>{let s=await lt(I);M.value=s.data.data};let q=r([]);(async()=>{let s=await xe();q.value=s.data})();const C=V({merchantGuid:"",cate_type:""}),B=r([]);let S=r(!1);const h=async()=>{S.value=!0;let s=await Le(C);B.value=s.data,S.value=!1},W=()=>{g.value=!0,k.value="add",T(),F()};let D=r("");const X=V([{id:0,type:"",name:"全部"},{id:1,type:"text",name:"文本"},{id:2,type:"image",name:"图像"}]),Y=s=>{D.value=s,C.cate_type=s},ee=()=>{h()};let g=r(!1),o=V({pid:0,title:"",image:"",desc:"",sort:0,status:200,chatgtp_content:"",cate_type:"text",merchantGuid:"",ai_model:"",able_lunci:1,build_knowledge_guids:[],knowledge_use_params:"针对用户的问题，我为你提供了知识库内容，知识库内容中的score代表了匹配度，满分是0.99，分数大于0.3就必须高度重视'，越高越匹配，content代表了知识库的内容。请你参考知识库的内容进行回答，尽量使用我为你提供的知识库内容，知识库内容是："});const te=r(),le=s=>{let n=["image/jpg","image/png","image/jpeg"];if(n.includes(s.type)){if(s.size/1024/1024>2)return b.error("图片必须小于2M"),!1}else return b.warning("当前图片仅支持格式为："+n.join(" ，")),!1;return!0},ae=async s=>{const n=new FormData;n.append("img",s.file);let u=await Re(n);o.image=u.data},oe=()=>{o.image=""},G=r(),ne=async s=>{s.validate(async n=>{if(n)try{k.value==="add"?(await Ie(o),b.success("新增成功")):k.value==="edit"&&(await Me(o),b.success("修改成功")),Fe(()=>{s.resetFields(),g.value=!1}),h()}catch(u){throw b.error(u),new Error(u)}})};let k=r("");const se=s=>{o=Object.assign(o,s),o.merchantGuid=s.merchant_guid,g.value=!0,k.value="edit",ue(),F()},ie=async s=>{let n={id:s.id};await qe(n),b.success("删除成功"),h()},de=()=>{o.cate_type==="image"&&(o.build_knowledge_guids=[]),T()},y=r([]),T=async()=>{let s=await Q(o);y.value=s.data,y.value.push({title:"无(一级)",id:0}),o.pid=y.value[0].id},ue=async()=>{let s=await Q(o);y.value=s.data};h();let m=V({isShow:!1,url:"",title:""});const re=async s=>{m.url="",m.title=s.title,m.isShow=!0;let n=await Be({id:s.id});m.url=n.data};return(s,n)=>{const u=De,i=Ge,$=Te,_=$e,N=Ne,E=je,pe=He,_e=Pe,ce=Ce,j=Oe,H=Ke,P=Je,O=Qe,U=Ze,me=ot,z=We,ge=nt,fe=Xe,ve=Ye,we=Ee("v-md-editor"),be=et,K=Se,ye=rt,Ve=tt;return d(),c("div",null,[e(ce,{class:"wrapper"},{default:l(()=>[e(_e,null,{default:l(()=>[v("div",pt,[e($,{inline:!0,model:a(C),class:"demo-form-inline"},{default:l(()=>[e(i,null,{default:l(()=>[e(u,{type:"primary",onClick:W},{default:l(()=>[p("新增场景")]),_:1})]),_:1}),e(i,{label:"场景类型"},{default:l(()=>[v("div",_t,[(d(!0),c(x,null,L(a(X),(t,w)=>(d(),c("div",{class:Ue(["item",{active:a(D)===t.type}]),key:w,onClick:ht=>Y(t.type)},A(t.name),11,ct))),128))])]),_:1}),e(i,null,{default:l(()=>[e(u,{type:"primary",onClick:ee},{default:l(()=>[p("搜索")]),_:1})]),_:1})]),_:1},8,["model"])]),J((d(),f(pe,{data:a(B),border:"",style:{width:"100%"},"row-key":"id","tree-props":{children:"child"}},{default:l(()=>[e(_,{prop:"id",label:"场景id",width:"120"}),e(_,{prop:"title",label:"名称",width:"150"}),e(_,{prop:"desc",label:"描述",width:"150"}),e(_,{prop:"image","show-overflow-tooltip":!0,label:"图片",width:"100"},{default:l(t=>[e(N,{src:t.row.image,style:{width:"50px",height:"50px","border-radius":"50%"}},null,8,["src"])]),_:1}),e(_,{prop:"sort",label:"排序",width:"50"}),e(_,{prop:"status",label:"状态",width:"150"},{default:l(t=>[e(E,{modelValue:t.row.status,"onUpdate:modelValue":w=>t.row.status=w,disabled:"","active-text":"正常","inactive-text":"隐藏","active-value":200,"inactive-value":100},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(_,{prop:"chatgtp_content",label:"提示语",width:"250","show-overflow-tooltip":!0}),e(_,{prop:"cate_type",label:"类型",width:"100"},{default:l(t=>[p(A(t.row.cate_type==="image"?"图片":"文本"),1)]),_:1}),e(_,{label:"操作"},{default:l(t=>[t.row.pid>0?(d(),f(u,{key:0,size:"small",type:"primary",onClick:w=>re(t.row)},{default:l(()=>[p("生成入口")]),_:2},1032,["onClick"])):R("",!0),e(u,{size:"small",type:"primary",onClick:w=>se(t.row)},{default:l(()=>[p("编辑")]),_:2},1032,["onClick"]),e(u,{size:"small",type:"warning",onClick:w=>ie(t.row)},{default:l(()=>[p("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Ve,a(S)]])]),_:1})]),_:1}),e(K,{modelValue:a(g),"onUpdate:modelValue":n[12]||(n[12]=t=>ke(g)?g.value=t:g=t),title:"新增/编辑 场景",width:"1000px"},{default:l(()=>[e($,{ref_key:"addForm",ref:G,model:a(o),class:"demo-form-inline","label-width":"100px"},{default:l(()=>[e(i,{label:"类型",prop:"cate_type",onChange:de},{default:l(()=>[e(H,{modelValue:a(o).cate_type,"onUpdate:modelValue":n[0]||(n[0]=t=>a(o).cate_type=t)},{default:l(()=>[e(j,{label:"text"},{default:l(()=>[p("文本")]),_:1}),e(j,{label:"image"},{default:l(()=>[p("图片")]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(i,{label:"上级分类",prop:"pid"},{default:l(()=>[e(O,{modelValue:a(o).pid,"onUpdate:modelValue":n[1]||(n[1]=t=>a(o).pid=t),placeholder:"请选择"},{default:l(()=>[(d(!0),c(x,null,L(a(y),t=>(d(),f(P,{label:t.title,value:t.id,key:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(i,{label:"名称",prop:"title"},{default:l(()=>[e(U,{modelValue:a(o).title,"onUpdate:modelValue":n[2]||(n[2]=t=>a(o).title=t),placeholder:"名称"},null,8,["modelValue"])]),_:1}),e(i,{label:"头像",prop:"iamge"},{default:l(()=>[e(fe,{ref_key:"uploadImgRef",ref:te,class:"avatar-uploader","before-upload":le,"show-file-list":!1,"http-request":ae},{default:l(()=>[v("div",mt,[a(o).image?(d(),c("img",{key:0,src:a(o).image,class:"preview-img"},null,8,gt)):(d(),c("div",ft,[e(z,{size:"30",color:"#cdd0d6"},{default:l(()=>[e(me)]),_:1})])),J(v("div",vt,[e(z,{size:"22",color:"#ffffff",onClick:Ae(oe,["stop"])},{default:l(()=>[e(ge)]),_:1},8,["onClick"])],512),[[ze,a(o).image]])])]),_:1},512)]),_:1}),e(i,{label:"描述",prop:"desc"},{default:l(()=>[e(U,{modelValue:a(o).desc,"onUpdate:modelValue":n[3]||(n[3]=t=>a(o).desc=t),placeholder:"描述"},null,8,["modelValue"])]),_:1}),e(i,{label:"排序",prop:"sort"},{default:l(()=>[e(ve,{modelValue:a(o).sort,"onUpdate:modelValue":n[4]||(n[4]=t=>a(o).sort=t),min:0},null,8,["modelValue"])]),_:1}),e(i,{label:"状态",prop:"status"},{default:l(()=>[e(E,{modelValue:a(o).status,"onUpdate:modelValue":n[5]||(n[5]=t=>a(o).status=t),"active-text":"正常","inactive-text":"隐藏","active-value":200,"inactive-value":100},null,8,["modelValue"])]),_:1}),e(i,{label:"提示语",prop:"chatgtp_content"},{default:l(()=>[e(we,{modelValue:a(o).chatgtp_content,"onUpdate:modelValue":n[6]||(n[6]=t=>a(o).chatgtp_content=t),height:"400px"},null,8,["modelValue"])]),_:1}),a(o).cate_type==="text"?(d(),f(i,{key:0,label:"选择知识库",prop:"build_knowledge_guids"},{default:l(()=>[e(O,{modelValue:a(o).build_knowledge_guids,"onUpdate:modelValue":n[7]||(n[7]=t=>a(o).build_knowledge_guids=t),placeholder:"请选择",multiple:"","multiple-limit":1},{default:l(()=>[(d(!0),c(x,null,L(a(M),t=>(d(),f(P,{label:t.knowledgeTitle,value:t.guid,key:t.guid},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):R("",!0),a(o).cate_type==="text"?(d(),f(i,{key:1,label:"知识库参数",prop:"knowledge_use_params"},{default:l(()=>[e(U,{modelValue:a(o).knowledge_use_params,"onUpdate:modelValue":n[8]||(n[8]=t=>a(o).knowledge_use_params=t)},null,8,["modelValue"])]),_:1})):R("",!0),e(i,{label:"选择Ai模型",prop:"ai_model"},{default:l(()=>[e(H,{modelValue:a(o).ai_model,"onUpdate:modelValue":n[9]||(n[9]=t=>a(o).ai_model=t),class:"input"},{default:l(()=>[(d(!0),c(x,null,L(a(q),t=>(d(),f(be,{label:t.id,size:"large",key:t.id},{default:l(()=>[p(A(t.name),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(i,{prop:"able_lunci",label:"是否开启上下文"},{default:l(()=>[e(E,{modelValue:a(o).able_lunci,"onUpdate:modelValue":n[10]||(n[10]=t=>a(o).able_lunci=t),"active-text":"开启","inactive-text":"关闭","active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1}),e(i,null,{default:l(()=>[e(u,{type:"primary",onClick:n[11]||(n[11]=t=>ne(a(G)))},{default:l(()=>[p("提交")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),e(K,{modelValue:a(m).isShow,"onUpdate:modelValue":n[13]||(n[13]=t=>a(m).isShow=t),title:a(m).title,width:"300px","align-center":""},{default:l(()=>[v("div",wt,[e(N,{style:{width:"200px",height:"200px"},src:a(m).url},{error:l(()=>[v("div",bt,[e(z,null,{default:l(()=>[e(ye)]),_:1})])]),_:1},8,["src"])])]),_:1},8,["modelValue","title"])])}}});const $t=at(yt,[["__scopeId","data-v-c3036e2d"]]);export{$t as default};
