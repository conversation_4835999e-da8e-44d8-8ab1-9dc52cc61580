<template>
  <div>
    <el-container class="wrapper">
      <el-main v-loading="loading">
        <div class="hearder-box">
          <el-form :inline="true" :model="reqForm" class="demo-form-inline">
            <el-form-item label="审核状态">
              <div class="mode-box">
                <div :class="['item', { active: modeStatus === item.type }]" v-for="(item, index) in modeList"
                  :key="index" @click="onChangeMode(item.type)">
                  {{ item.name }}
                </div>
              </div>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSearch">搜索</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table :data="goodsList" border style="width: 100%">
          <el-table-column prop="user.sysId" label="用户Id" width="80" />
          <el-table-column prop="user.nickname" label="用户昵称" width="180" />
          <el-table-column prop="user.mobile" label="手机号" width="120" />
          <el-table-column prop="applyRemark" :show-overflow-tooltip="true" label="申请备注" width="120" />
          <el-table-column prop="statusText" label="审核状态" />
          <el-table-column prop="refuseReason" label="拒绝理由" />
          <el-table-column prop="createTime" label="申请时间" />
          <el-table-column label="操作">
            <template #default="scope">
              <el-button size="small" type="primary" @click="onShowApprove(scope.row)">审批</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-main>
      <el-footer>
        <el-pagination background layout="prev,pager, next" :total="total" :current-page="reqForm.page"
          @current-change="handlePageChang" />
      </el-footer>
    </el-container>
    <el-dialog v-model="approveVisible" title="Shipping address" width="500">
      <el-form :model="applyForm">
        <el-form-item label="审批状态" label-width="80px">
          <el-select v-model="applyForm.applyStatus">
            <el-option :label="item.name" :value="item.type" v-for="item in modeList" />
          </el-select>
        </el-form-item>
        <el-form-item label="拒绝理由" label-width="80px">
          <el-input v-model="applyForm.refuseReason" autocomplete="off" placeholder="非拒绝则不填" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="approveVisible = false">取 消</el-button>
          <el-button type="primary" @click="onApprove"> 确 认 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { getBigVipApplyList, getTenantListApi, approveBigVipApply } from '@/api';
//分页
const total = ref(0);
const loading = ref(false);
let modeStatus = ref('');
const modeList = reactive([
  {
    id: 3,
    type: '',
    name: '全部',
  },
  {
    id: 0,
    type: '100',
    name: '待审核',
  },
  {
    id: 1,
    type: '200',
    name: '审核通过',
  },
  {
    id: 2,
    type: '300',
    name: '审核拒绝',
  },
]);
const reqForm = reactive({
  merchantGuid: '',
  applyStatus: '',
  pageSize: 10,
  page: 1,
});
let goodsList = ref([]);
const onChangeMode = (value) => {
  modeStatus.value = value;
  reqForm.applyStatus = value;
  getList();
};
//获取申请列表
const getList = async () => {
  loading.value = true;
  let goodsRes = await getBigVipApplyList(reqForm);
  loading.value = false;
  total.value = goodsRes.data.total;
  goodsList.value = goodsRes.data.data;
};

const handlePageChang = async (page) => {
  reqForm.page = page;
  getList();
};
const applyForm = reactive({
  guid: '',
  applyStatus: '200',
  refuseReason: '',
});
const approveVisible = ref(false);
const onShowApprove = (item) => {
  applyForm.guid = item.guid;
  approveVisible.value = true;
};
const onApprove = async () => {
  try {
    await approveBigVipApply(applyForm);
    ElMessage({
      message: '审核通过',
      type: 'success',
    });
    getList();
  } catch (error) { }
};
//搜索
const onSearch = () => {
  getList();
};
getList();
</script>
<style scoped lang="scss">
.mode-box {
  background-color: #eee;
  display: flex;
  padding: 4px 0px;
  border-radius: 6px;
  width: 400px;

  .item {
    background-color: #fff;
    //padding: 0 5px;
    margin: 0 5px;
    flex: 1;
    border-radius: 6px;
    text-align: center;
    cursor: pointer;
    height: 26px;
    line-height: 26px;

    &.active {
      background-color: #409eff;
      color: #fff;
    }
  }
}
</style>
