<script setup lang="ts">
import { getChatGoodsApi, getTenantListApi, checkTenantChatPayApi, queryOrderAmountApi } from '@/api';

//分页
const total = ref(0);
const loading = ref(false);
const orderAmount = ref(0);
const reqForm = reactive({
  orderNo: '',
  orderStatus: '',
  merchantGuid: '',
  orderTime: '',
  parentUid: '',
  pageSize: 10,
  page: 1,
});
let goodsList = ref([]);
let tenantList: any = ref([]);
//获取产品列表
const getChatGoods = async () => {
  loading.value = true;
  let goodsRes = await getChatGoodsApi(reqForm);
  let queryRes = await queryOrderAmountApi(reqForm);
  orderAmount.value = queryRes.data;
  loading.value = false;
  total.value = goodsRes.data.total;
  goodsList.value = goodsRes.data.data;
};
//获取商户列表
const getTenantList = async () => {
  let res = await getTenantListApi();
  tenantList.value = res.data;
};
const handlePageChang = async (page) => {
  reqForm.page = page;
  getChatGoods();
};
//查询支付
const onCheck = async (item) => {
  let res = await checkTenantChatPayApi({
    orderNo: item.orderNo,
  });
  let text = res.data.isPay ? '已成功支付' : '未支付';
  ElMessageBox.alert(text, '查询结果', {
    confirmButtonText: '确定',
  });
};
//搜索
const onSearch = () => {
  getChatGoods();
};
getChatGoods();
getTenantList();
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main v-loading="loading">
        <div class="hearder-box">
          <el-form :inline="true" :model="reqForm" class="demo-form-inline">
            <el-form-item label="订单编号">
              <el-input v-model="reqForm.orderNo" placeholder="订单编号" />
            </el-form-item>
            <el-form-item label="上级用户Id">
              <el-input v-model="reqForm.parentUid" placeholder="上级用户Id" />
            </el-form-item>
            <el-form-item label="支付状态">
              <el-select v-model="reqForm.orderStatus" placeholder="请选择">
                <el-option label="全部" value="" />
                <el-option label="待支付" value="100" />
                <el-option label="已支付" value="200" />
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="所属商家">
              <el-select v-model="reqForm.merchantGuid" placeholder="请选择">
                <el-option label="无" value="" />
                <el-option :label="item.merchantName" :value="item.guid" v-for="item in tenantList" :key="item.guid" />
              </el-select>
            </el-form-item> -->
            <el-form-item label="订单日期">
              <!-- <div class="demo-time-range"> -->
              <el-date-picker v-model="reqForm.orderTime" type="daterange" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                start-placeholder="开始日期" end-placeholder="结束日期" />
            </el-form-item>
            <el-form-item label="订单总金额">
              {{ orderAmount }}
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSearch">搜索</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table :data="goodsList" border style="width: 100%">
          <el-table-column prop="platformUserSysId" label="订单Id" width="80" />
          <el-table-column prop="orderNo" label="订单编号" />
          <el-table-column prop="chatCount" label="聊天点数" width="120" />
          <el-table-column prop="amount" label="订单金额" width="120" />
          <el-table-column prop="nickname" label="支付用户" />
          <el-table-column prop="orderStatusText" label="支付状态">
            <template #default="scope">
              {{ scope.row.orderStatusText }}
              <el-button v-if="scope.row.orderStatusText === '待支付'" size="small" type="primary"
                @click="onCheck(scope.row)">查询</el-button>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="下单时间" />
          <el-table-column prop="modifyTime" label="付款时间" />
        </el-table>
      </el-main>
      <el-footer>
        <el-pagination background layout="prev,pager, next" :total="total" :current-page="reqForm.page"
          @current-change="handlePageChang" />
      </el-footer>
    </el-container>
  </div>
</template>

<style scoped lang="scss"></style>
