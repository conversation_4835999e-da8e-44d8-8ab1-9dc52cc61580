<script setup lang="ts">
import { getCardOrderList, cardOrderQueryBuy } from '@/api';
//分页
const total = ref(0);
const loading = ref(false);
let modeStatus = ref('');
const modeList = reactive([
  {
    id: 4,
    type: '',
    name: '全部',
  },
  {
    id: 0,
    type: '100',
    name: '待支付',
  },
  {
    id: 1,
    type: '200',
    name: '已支付',
  },
  {
    id: 2,
    type: '300',
    name: '取消支付',
  },
  {
    id: 3,
    type: '400',
    name: '支付超时',
  },
]);
const reqForm = reactive({
  merchantGuid: '',
  nickname: '',
  orderStatus: '',
  orderNo: '',
  pageSize: 10,
  page: 1,
});
let goodsList = ref([]);
const onChangeMode = (value) => {
  modeStatus.value = value;
  reqForm.orderStatus = value;
};
//获取产品列表
const getList = async () => {
  loading.value = true;
  let goodsRes = await getCardOrderList(reqForm);
  loading.value = false;
  total.value = goodsRes.data.total;
  goodsList.value = goodsRes.data.data;
};
const handlePageChang = async (page) => {
  reqForm.page = page;
  getList();
};
//搜索
const onSearch = () => {
  getList();
};
const onCheck = async (item) => {
  let res = await cardOrderQueryBuy({ orderNo: item.orderNo });
  ElMessage({
    message: res.data.msg,
    type: 'success',
  });
  getList();
};
getList();
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main v-loading="loading">
        <div class="hearder-box">
          <el-form :inline="true" :model="reqForm" class="demo-form-inline">
            <el-form-item label="用户昵称">
              <el-input v-model="reqForm.nickname" placeholder="用户昵称" />
            </el-form-item>
            <el-form-item label="订单号">
              <el-input v-model="reqForm.orderNo" placeholder="手机号码" />
            </el-form-item>
            <el-form-item label="支付状态">
              <div class="mode-box">
                <div :class="['item', { active: modeStatus === item.type }]" v-for="(item, index) in modeList"
                  :key="index" @click="onChangeMode(item.type)">
                  {{ item.name }}
                </div>
              </div>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSearch">搜索</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table :data="goodsList" border style="width: 100%">
          <el-table-column prop="user.sysId" label="用户Id" width="80" />
          <el-table-column prop="user.nickname" label="用户昵称" width="180" />
          <el-table-column prop="user.mobile" label="手机号" width="120" />
          <el-table-column prop="orderNo" label="订单号" width="120" />
          <el-table-column prop="cardTypeText" label="会员卡类型" width="120" />
          <el-table-column prop="cardPrice" label="会员卡价格" />
          <el-table-column prop="payAmount" label="支付金额" />
          <el-table-column prop="orderStatusText" label="支付状态" width="120" />
          <el-table-column prop="payTypeText" label="支付方式" width="120" />
          <el-table-column prop="createTime" label="创建时间" />
          <el-table-column label="操作">
            <template #default="scope">
              <el-button size="small" type="primary" @click="onCheck(scope.row)">查询支付结果</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-main>
      <el-footer>
        <el-pagination background layout="prev,pager, next" :total="total" :current-page="reqForm.page"
          @current-change="handlePageChang" />
      </el-footer>
    </el-container>
  </div>
</template>

<style scoped lang="scss">
.mode-box {
  background-color: #eee;
  display: flex;
  padding: 4px 0px;
  border-radius: 6px;
  width: 400px;

  .item {
    background-color: #fff;
    //padding: 0 5px;
    margin: 0 5px;
    flex: 1;
    border-radius: 6px;
    text-align: center;
    cursor: pointer;
    height: 26px;
    line-height: 26px;

    &.active {
      background-color: #409eff;
      color: #fff;
    }
  }
}
</style>
