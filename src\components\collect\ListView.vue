<script setup lang="ts">
import { getCollectListApi, getTenantListApi } from '@/api';
//分页
const total = ref(0);
const loading = ref(false);
const reqForm = reactive({
  merchantGuid: '',
  nickname: '',
  pageSize: 10,
  page: 1,
});
let goodsList = ref([]);
let tenantList: any = ref([]);
//弹窗相关
let dialogVisible = ref(false);
let dialogContent = reactive({
  title: '',
  content: '',
  nickname: '',
});
//获取产品列表
const getList = async () => {
  loading.value = true;
  let goodsRes = await getCollectListApi(reqForm);
  loading.value = false;
  total.value = goodsRes.data.total;
  goodsList.value = goodsRes.data.data;
};
//获取商户列表
const getTenantList = async () => {
  let res = await getTenantListApi();
  tenantList.value = res.data;
};
const handlePageChang = async (page) => {
  reqForm.page = page;
  getList();
};
//搜索
const onSearch = () => {
  getList();
};
const handleEdit = (item) => {
  console.log(item, 'itemitem');
  dialogContent.title = item.chatTitle;
  dialogContent.content = item.chatContent;
  dialogContent.nickname = item.user.nickname;
  dialogVisible.value = true;
};
getList();
getTenantList();
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main v-loading="loading">
        <div class="hearder-box">
          <el-form :inline="true" :model="reqForm" class="demo-form-inline">
            <el-form-item label="用户昵称">
              <el-input v-model="reqForm.nickname" placeholder="用户昵称" />
            </el-form-item>
            <!-- <el-form-item label="所属商家">
              <el-select v-model="reqForm.merchantGuid" placeholder="请选择">
                <el-option label="无" value="" />
                <el-option :label="item.merchantName" :value="item.guid" v-for="item in tenantList" :key="item.guid" />
              </el-select>
            </el-form-item> -->
            <el-form-item>
              <el-button type="primary" @click="onSearch">搜索</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table :data="goodsList" border style="width: 100%">
          <el-table-column prop="user.sysId" label="用户Id" width="80" />
          <el-table-column prop="user.nickname" label="用户昵称" width="180" />
          <el-table-column prop="chatTitle" :show-overflow-tooltip="true" label="收藏标题" width="220" />
          <el-table-column prop="chatContent" :show-overflow-tooltip="true" label="收藏内容" width="220" />
          <el-table-column prop="modifyTime" label="收藏时间" />
          <el-table-column label="操作">
            <template #default="scope">
              <el-button size="small" type="primary" @click="handleEdit(scope.row)">查看详情</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-main>
      <el-footer>
        <el-pagination background layout="prev,pager, next" :total="total" :current-page="reqForm.page"
          @current-change="handlePageChang" />
      </el-footer>
    </el-container>
    <el-dialog v-model="dialogVisible" :title="dialogContent.nickname">
      <div class="harder">
        {{ dialogContent.title }}
      </div>
      <div class="body">
        {{ dialogContent.content }}
      </div>
      <!-- <el-card>
        <template #header>
          <div class="card-header">
            
          </div>
        </template>
        {{ dialogContent.content }}
      </el-card> -->
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.harder {
  font-size: 16px;
  padding-bottom: 20px;
}

.body {
  font-size: 18px;
}
</style>
