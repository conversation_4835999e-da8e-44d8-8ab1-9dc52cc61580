<script setup lang="ts">
import { expoListApi, changeExpoStatusApi, rechargePointApi } from '@/api';
import { useRouter } from 'vue-router';
import { useAdminCommonStore } from '@/stores/adminCommon';
const store = useAdminCommonStore();
const router = useRouter();
//分页
const total = ref(0);
const loading = ref(false);
const reqForm = reactive({
  merchantGuid: '',
  pageSize: 10,
  page: 1,
});
let merchantGuid = computed(() => {
  return store.getTenantInfo.guid;
});
let goodsList = ref([]);
//获取产品列表
const getList = async () => {
  loading.value = true;
  reqForm.merchantGuid = merchantGuid.value;
  let goodsRes = await expoListApi(reqForm);
  loading.value = false;
  total.value = goodsRes.data.total;
  goodsList.value = goodsRes.data.data;
};
const handlePageChang = async (page) => {
  reqForm.page = page;
  getList();
};
const onEdit = (item) => {
  router.push({
    name: 'setExpoInfo',
    query: {
      guid: item.guid,
      type: 'EDIT',
    },
  });
};
const onAdd = () => {
  router.push({
    name: 'setExpoInfo',
    query: {
      type: 'ADD',
    },
  });
};
const onSetQuestion = (item) => {
  router.push({
    name: 'expoFaqList',
    query: {
      guid: item.guid,
      merchantGuid: 'e108201b02ae42e686bcc4c302cbbd11',
    },
  });
};
const onSetBanner = (item) => {
  router.push({
    name: 'expoBannerList',
    query: {
      guid: item.guid,
      merchantGuid: 'e108201b02ae42e686bcc4c302cbbd11',
    },
  });
};
let pointEditVisible = ref(false);
const addPointReq = reactive({
  guid: '',
  rechargePoint: 1,
});
// const onPointEdit = (item) => {
//   pointEditVisible.value = true;
//   addPointReq.guid = item.guid;
// };
const onAddPointSave = async () => {
  await rechargePointApi(addPointReq);
  ElMessage.success('增加成功');
  pointEditVisible.value = false;
  getList();
};
const onDisable = async (item) => {
  let req = {
    guid: item.guid,
  };
  await changeExpoStatusApi(req);
  ElMessage.success('修改状态成功');
  getList();
};
//搜索
const onSearch = () => {
  getList();
};
getList();
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main v-loading="loading">
        <div class="hearder-box">
          <el-form :inline="true" :model="reqForm" class="demo-form-inline">
            <el-form-item>
              <el-button type="primary" @click="onAdd">新增</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table :data="goodsList" border style="width: 100%">
          <el-table-column prop="sysId" label="sysId" width="80" />
          <el-table-column prop="name" label="展会名称" />
          <el-table-column prop="startTime" label="展会开始时间/截止时间">
            <template #default="scope"> {{ scope.row.startTime }} - {{ scope.row.endTime }} </template>
          </el-table-column>
          <el-table-column prop="aiPoint" label="AI点数余额">
            <!-- <template #default="scope">
              {{ scope.row.aiPoint }}
              <el-button size="small" type="primary" @click="onPointEdit(scope.row)">增加</el-button>
            </template> -->
          </el-table-column>
          <el-table-column prop="showStatus" label="展示状态">
            <template #default="scope"> {{ scope.row.showStatus ? '展示中' : '未展示' }} </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" min-width="250">
            <template #default="scope">
              <el-button size="small" type="primary" @click="onEdit(scope.row)">编辑</el-button>
              <el-popconfirm confirm-button-text="确认" cancel-button-text="取消" icon-color="red"
                :title="scope.row.status == 2 ? '是否启用展会' : '是否禁用展会'" @confirm="onDisable(scope.row)">
                <template #reference>
                  <el-button size="small" :type="scope.row.status == 2 ? 'success' : 'danger'">{{
                    scope.row.status == 2 ? '启用' : '禁用'
                  }}</el-button>
                </template>
              </el-popconfirm>
              <el-button size="small" type="primary" @click="onSetQuestion(scope.row)">常见问题设置</el-button>
              <el-button size="small" type="primary" @click="onSetBanner(scope.row)">轮播图设置</el-button>
              <el-button size="small" type="primary">展会知识库</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-main>
      <el-footer>
        <el-pagination background layout="prev,pager, next" :total="total" :current-page="reqForm.page"
          @current-change="handlePageChang" />
      </el-footer>
    </el-container>
    <el-dialog v-model="pointEditVisible" title="增加点数" width="20%" center>
      <div class="add-point-box">
        <el-input-number v-model="addPointReq.rechargePoint" :min="1" />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="pointEditVisible = false">取消</el-button>
          <el-button type="primary" @click="onAddPointSave"> 确认 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.add-point-box {
  display: flex;
  justify-content: center;
}
</style>
