import{d as V,r as c,a as C,c as I,b as e,w as a,bn as L,e as B,o as m,T as F,h as o,I as N,f as S,i as q,k as M,l as P,p as U,q as z,s as D,v as G,y as R,x as Y,Y as Z,Z as j,_ as A}from"./index-6e8b0ade.js";/* empty css                   *//* empty css                      *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                     */const H={class:"hearder-box"},J=V({__name:"userListView",setup(K){const i=c(0),s=c(!1),n=C({merchantGuid:"",nickname:"",pageSize:10,page:1});let _=c([]);const r=async()=>{s.value=!0;let l=await L(n);s.value=!1,i.value=l.data.total,_.value=l.data.data},u=async l=>{n.page=l,r()},f=()=>{r()};return r(),(l,p)=>{const g=M,d=P,b=U,h=z,t=D,v=G,w=R,E=Y,y=Z,k=B,x=j;return m(),I("div",null,[e(k,{class:"wrapper"},{default:a(()=>[F((m(),N(w,null,{default:a(()=>[S("div",H,[e(h,{inline:!0,model:o(n),class:"demo-form-inline"},{default:a(()=>[e(d,{label:"用户昵称"},{default:a(()=>[e(g,{modelValue:o(n).nickname,"onUpdate:modelValue":p[0]||(p[0]=T=>o(n).nickname=T),placeholder:"用户昵称"},null,8,["modelValue"])]),_:1}),e(d,null,{default:a(()=>[e(b,{type:"primary",onClick:f},{default:a(()=>[q("搜索")]),_:1})]),_:1})]),_:1},8,["model"])]),e(v,{data:o(_),border:"",style:{width:"100%"}},{default:a(()=>[e(t,{prop:"user.sysId",label:"用户Id",width:"80"}),e(t,{prop:"user.nickname",label:"用户昵称",width:"180"}),e(t,{prop:"user.mobile",label:"手机号",width:"120"}),e(t,{prop:"cardTypeText",label:"类型",width:"120"}),e(t,{prop:"createTime",label:"购买时间"}),e(t,{prop:"cardStartTime",label:"开始时间"}),e(t,{prop:"cardEndTime",label:"结束时间"})]),_:1},8,["data"])]),_:1})),[[x,o(s)]]),e(y,null,{default:a(()=>[e(E,{background:"",layout:"prev,pager, next",total:o(i),"current-page":o(n).page,onCurrentChange:u},null,8,["total","current-page"])]),_:1})]),_:1})])}}});const oe=A(J,[["__scopeId","data-v-0445ac41"]]);export{oe as default};
