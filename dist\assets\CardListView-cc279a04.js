import{d as y,r as s,a as L,c as k,b as a,w as n,aV as E,S as V,e as N,o as u,T,h as l,I as B,f as P,U as S,aC as W,s as z,N as A,v as D,y as F,x as R,Y as q,Z as G,_ as J}from"./index-6e8b0ade.js";/* empty css                   *//* empty css                      *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                        */const M=["onClick"],U=y({__name:"CardListView",setup(Y){const p=s(0),r=s(!1),i=L({merchantGuid:"",pageSize:10,page:1});let c=s([]),f=s([]);const d=async()=>{r.value=!0;let e=await E(i);r.value=!1,p.value=e.data.total,c.value=e.data.data},m=async()=>{let e=await V();f.value=e.data},g=async e=>{i.page=e,d()},w=e=>{e.length>0&&window.open(e,"_blank")};return d(),m(),(e,Z)=>{const t=z,_=A,v=D,b=F,h=R,I=q,C=N,x=G;return u(),k("div",null,[a(C,{class:"wrapper"},{default:n(()=>[T((u(),B(b,null,{default:n(()=>[a(v,{data:l(c),border:"",style:{width:"100%"}},{default:n(()=>[a(t,{prop:"sysId",label:"分身Id",width:"80"}),a(t,{prop:"separationInfo.baseRealName",label:"分身昵称",width:"120"}),a(t,{prop:"separationInfo.avatar",label:"分身头像",width:"100"},{default:n(o=>[a(_,{src:o.row.separationInfo.avatar,style:{width:"50px",height:"50px","border-radius":"50%"}},null,8,["src"])]),_:1}),a(t,{prop:"separationInfo.baseCompany",label:"公司"}),a(t,{prop:"separationInfo.baseJobPosition",label:"职位"}),a(t,{prop:"separationInfo.contactPhone",label:"电话"}),a(t,{prop:"separationInfo.contactWxNumber",label:"微信号"}),a(t,{prop:"separationInfo.contactWxImg",label:"微信码"},{default:n(o=>[a(_,{src:o.row.separationInfo.contactWxImg,style:{width:"50px",height:"50px","border-radius":"50%"}},null,8,["src"])]),_:1}),a(t,{prop:"separationInfo.video","show-overflow-tooltip":!0,label:"视频",width:"100"},{default:n(o=>[P("div",{onClick:$=>w(o.row.separationInfo.video),class:W(["video-link",{active:o.row.separationInfo.video}])},S(o.row.separationInfo.video?"点击查看":""),11,M)]),_:1}),a(t,{prop:"separationInfo.resumeInfo",label:"履历","show-overflow-tooltip":!0})]),_:1},8,["data"])]),_:1})),[[x,l(r)]]),a(I,null,{default:n(()=>[a(h,{background:"",layout:"prev,pager, next",total:l(p),"current-page":l(i).page,onCurrentChange:g},null,8,["total","current-page"])]),_:1})]),_:1})])}}});const ta=J(U,[["__scopeId","data-v-670dd511"]]);export{ta as default};
