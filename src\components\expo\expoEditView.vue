<script setup lang="ts">
import { useRoute } from 'vue-router';
import { uploadImg, createExpoApi, updateExpoApi, getExpoDetailApi, getKnowLedgeListApi } from '@/api';
import type { UploadInstance, UploadProps, FormInstance, FormRules } from 'element-plus';

const route = useRoute();
let expoForm = reactive({
  merchantGuid: '',
  name: '', //展会名称
  shortName: '', //展会简称
  logo: '', //展会logo
  slogo: '', //展会标语
  cover: '', //展会封面
  showOrder: 1,
  description: '', //展会描述
  startTime: '2024-09-01', //展会开始时间
  endTime: '2024-10-01', //展会结束时间
  showTime: 1, //是否显示时间
  endIsShow: 1, //结束是否展示
  welcomeText: '欢迎来到 2024年设宇宙商协通大会 !我是您的智能助手。 请问有什么可以帮助您的吗', //AI助理对话欢迎语
  isRequirePhone: 2, //是否强制校验手机号码：1-是；2-否；默认2
  aiChatPrompt:
    '请你作为展会助手AI，为用户提供热情、专业、耐心的服务。行为准则：友好热情： 以积极的态度回应用户，使用礼貌和鼓励性的语言。专业准确： 提供的信息需要准确无误，确保用户获得可靠的展会信息。耐心细致： 对于用户的问题，即使重复或复杂，也要耐心解答，不急躁。主动提供帮助： 在用户提问时，除了回答问题，还可以主动提供额外相关信息或建议。', //AI对话提示词
  merchantKnowledgeGuid: '', //商家知识库guid
  knowledgePrompt:
    '本次用户提问有匹配的知识库内容，请你参考知识库的内容进行回答，尽量使用我为你提供的知识库内容，知识库内容是：', //知识库引用提示词
});
const fromType = ref('ADD');
const addForm = ref<FormInstance>();
const rules = reactive<FormRules>({
  // merchantGuid: [{ required: true, message: '请选择商家', trigger: 'change' }],
  name: [{ required: true, message: '请输入展会名称', trigger: 'blur' }],
  shortName: [{ required: true, message: '请输入展会简称', trigger: 'blur' }],
  logo: [{ required: true, message: '请上传Logo', trigger: 'change' }],
  slogo: [{ required: true, message: '请输入展会标语', trigger: 'blur' }],
  description: [{ required: true, message: '请输入展会标语', trigger: 'blur' }],
  cover: [{ required: true, message: '请上传展会封面', trigger: 'change' }],
  nadescriptionme: [{ required: true, message: '请输入展会描述', trigger: 'blur' }],
  startTime: [{ required: true, message: '请选择展会开始时间', trigger: 'change' }],
  endTime: [{ required: true, message: '请选择展会结束时间', trigger: 'change' }],
  // endIsShow: [{ required: true, message: '请选择展会状态', trigger: 'change' }],
  // isRequirePhone: [{ required: true, message: '是否强制校验手机号码', trigger: 'change' }],
  // welcomeText: [{ required: true, message: '请输入AI助理对话欢迎语', trigger: 'blur' }],
  // aiChatPrompt: [{ required: true, message: '请输入AI对话提示词', trigger: 'blur' }],
  // merchantKnowledgeGuid: [{ required: true, message: '请选择知识库', trigger: 'change' }],
  // knowledgePrompt: [{ required: true, message: '请输入知识库引用提示词', trigger: 'blur' }],
});
const knowLedgeReq = reactive({
  merchantGuid: '',
  pageSize: 200,
  page: 1,
});
const total = ref(0);
const knowLedgeList: any = ref([]);
const getKnowLedgeList = async () => {
  let res = await getKnowLedgeListApi(knowLedgeReq);
  knowLedgeList.value = res.data.data;
  total.value = res.data.total;
};
const uploadImgRef = ref<UploadInstance>();
const beforeLogoUpload: UploadProps['beforeUpload'] = (rawFile) => {
  let fileTypes = ['image/jpg', 'image/png', 'image/jpeg'];
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.src = URL.createObjectURL(rawFile);
    if (!fileTypes.includes(rawFile.type)) {
      ElMessage.warning('当前图片仅支持格式为：' + fileTypes.join(' ，'));
      reject(false);
      return;
    }
    resolve(true);
    // img.onload = () => {
    //   if (img.width !== 90 || img.height !== 90) {
    //     // reject(new Error('图片尺寸超过限制'));
    //     ElMessage.warning('请上传90 X 90的图片');
    //     reject(false);
    //   } else {
    //     resolve(true);
    //   }
    // };
  });
};
const beforeLogoUpload2: UploadProps['beforeUpload'] = (rawFile) => {
  let fileTypes = ['image/jpg', 'image/png', 'image/jpeg'];
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.src = URL.createObjectURL(rawFile);
    if (!fileTypes.includes(rawFile.type)) {
      ElMessage.warning('当前图片仅支持格式为：' + fileTypes.join(' ，'));
      reject(false);
      return;
    }
    resolve(true);
    // img.onload = () => {
    //   if (img.width !== 550 || img.height !== 310) {
    //     // reject(new Error('图片尺寸超过限制'));
    //     ElMessage.warning('请上传550 X 310的图片');
    //     reject(false);
    //   } else {
    //     resolve(true);
    //   }
    // };
  });
};
const logoUpload = async (file) => {
  const formData = new FormData();
  // 添加要上传的文件名和文件对象,key就是你上传文件的字段名
  formData.append('img', file.file);
  let res = await uploadImg(formData);
  expoForm.logo = res.data;
};
const handleLogoRemove = () => {
  expoForm.logo = '';
};
const logoUpload2 = async (file) => {
  const formData = new FormData();
  // 添加要上传的文件名和文件对象,key就是你上传文件的字段名
  formData.append('img', file.file);
  let res = await uploadImg(formData);
  expoForm.cover = res.data;
};
const handleLogoRemove2 = () => {
  expoForm.cover = '';
};
const onSave = async (formEl) => {
  formEl.validate(async (valid) => {
    if (!valid) {
      return;
    }
    try {
      if (fromType.value === 'ADD') {
        await createExpoApi(expoForm);
        ElMessage.success('新增成功');
        nextTick(() => {
          formEl.resetFields();
        });
      } else if (fromType.value === 'EDIT') {
        await updateExpoApi(expoForm);
        ElMessage.success('修改成功');
      }
    } catch (error: any) {
      ElMessage.error(error);
      throw new Error(error);
    }
  });
};
const getExpoDetail = async () => {
  if (route.query.type === 'EDIT' && route.query.guid) {
    let req = {
      guid: route.query.guid,
    };
    let res = await getExpoDetailApi(req);
    expoForm = Object.assign(expoForm, res.data);
    knowLedgeReq.merchantGuid = res.data.merchantGuid;
    getKnowLedgeList();
  }
};
fromType.value = route.query.type as string;
watch(() => route.query.guid, (newGuid) => {
  if (newGuid && route.query.type === 'EDIT') {
    getExpoDetail();
  }
}, { immediate: false });
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main>
        <el-form ref="addForm" :model="expoForm" :rules="rules" class="form-box" label-width="200px">
          <el-form-item label="展会名称" prop="name">
            <el-input class="input" v-model="expoForm.name" placeholder="展会名称不要超过20字" maxlength="20" />
          </el-form-item>
          <el-form-item label="展会简称" prop="shortName">
            <el-input class="input" v-model="expoForm.shortName" placeholder="展会简称不要写太长(不然C端不好看)" />
          </el-form-item>
          <el-form-item label="排序" prop="showOrder">
            <el-input-number v-model="expoForm.showOrder" :min="1" />
          </el-form-item>
          <el-form-item label="主办方LOGO" prop="logo">
            <el-upload ref="uploadImgRef" class="avatar-uploader" :before-upload="beforeLogoUpload"
              :show-file-list="false" :http-request="logoUpload">
              <div class="upload-img-box">
                <img v-if="expoForm.logo" :src="expoForm.logo" class="preview-img" />
                <div class="upload-btn" v-else>
                  <el-icon size="30" color="#cdd0d6"><i-ep-Plus /></el-icon>
                </div>
                <div class="operate-box" v-show="expoForm.logo">
                  <el-icon size="22" color="#ffffff" @click.stop="handleLogoRemove"><i-ep-Delete /></el-icon>
                </div>
              </div>
              <template #tip>
                <div class="el-upload__tip" style="margin-top: 3px">上传尺寸为：90px * 90px的图片</div>
              </template>
            </el-upload>
          </el-form-item>
          <el-form-item label="展会封面图片" prop="cover">
            <el-upload ref="uploadImgRef2" class="avatar-uploader" :before-upload="beforeLogoUpload2"
              :show-file-list="false" :http-request="logoUpload2">
              <div class="upload-img-box">
                <img v-if="expoForm.cover" :src="expoForm.cover" class="preview-img" />
                <div class="upload-btn" v-else>
                  <el-icon size="30" color="#cdd0d6"><i-ep-Plus /></el-icon>
                </div>
                <div class="operate-box" v-show="expoForm.cover">
                  <el-icon size="22" color="#ffffff" @click.stop="handleLogoRemove2"><i-ep-Delete /></el-icon>
                </div>
              </div>
              <template #tip>
                <div class="el-upload__tip" style="margin-top: 3px">上传尺寸为：550px * 310px</div>
              </template>
            </el-upload>
          </el-form-item>
          <el-form-item label="展会标语" prop="slogo">
            <el-input v-model="expoForm.slogo" placeholder="展会标语不要超过10字" maxlength="20" />
          </el-form-item>
          <el-form-item label="展会简介" prop="description">
            <el-input v-model="expoForm.description" type="textarea" :rows="3" placeholder="展会简介不要超过100字"
              maxlength="100" />
          </el-form-item>
          <el-form-item label="展会开始时间" prop="startTime">
            <el-date-picker v-model="expoForm.startTime" type="date" placeholder="选择日期"> </el-date-picker>
          </el-form-item>
          <el-form-item label="展会结束时间" prop="endTime">
            <el-date-picker v-model="expoForm.endTime" type="date" placeholder="选择日期"> </el-date-picker>
          </el-form-item>
          <el-form-item label="是否显示展会时间" prop="showTime">
            <el-switch v-model="expoForm.showTime" :active-value="1" :inactive-value="0" active-color="#13ce66"
              inactive-color="#ff4949">
            </el-switch>
          </el-form-item>
          <el-form-item label="展会结束是否自动关闭展会" prop="endIsShow">
            <el-switch v-model="expoForm.endIsShow" :active-value="1" :inactive-value="2" active-color="#13ce66"
              inactive-color="#ff4949">
            </el-switch>
          </el-form-item>
          <el-form-item label="是否强制校验手机号码" prop="isRequirePhone">
            <el-switch v-model="expoForm.isRequirePhone" :active-value="1" :inactive-value="2" active-color="#13ce66"
              inactive-color="#ff4949">
            </el-switch>
          </el-form-item>
          <el-form-item label="AI助理对话欢迎语" prop="welcomeText">
            <el-input v-model="expoForm.welcomeText" type="textarea" :rows="3" placeholder="欢迎语不要超过100字"
              maxlength="100" />
          </el-form-item>
          <el-form-item label="AI对话提示词" prop="aiChatPrompt">
            <el-input v-model="expoForm.aiChatPrompt" type="textarea" :rows="5" placeholder="AI对话提示词" />
          </el-form-item>
          <el-form-item label="选择知识库" prop="merchantKnowledgeGuid">
            <el-select v-model="expoForm.merchantKnowledgeGuid" placeholder="请选择">
              <el-option :label="item.knowledgeTitle" :value="item.guid" v-for="item in knowLedgeList"
                :key="item.guid" />
            </el-select>
          </el-form-item>
          <el-form-item label="知识库引用提示词" prop="knowledgePrompt">
            <el-input v-model="expoForm.knowledgePrompt" type="textarea" :rows="3" placeholder="知识库引用提示词" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSave(addForm)">提交</el-button>
          </el-form-item>
        </el-form>
      </el-main>
    </el-container>
  </div>
</template>

<style scoped lang="scss">
.form-box {
  .input {
    width: 260px;
    margin-right: 18px;
  }
}

.avatar-uploader {
  margin-right: 20px;
}

.upload-img-box {
  width: 120px;
  height: 120px;
  border: 1px solid #cdd0d6;
  box-sizing: border-box;
  position: relative;
  border-radius: 6px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;

  .preview-img {
    width: 100%;
    height: 100%;
  }

  .operate-box {
    position: absolute;
    z-index: 9;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.5);
    visibility: hidden;
  }

  &:hover .operate-box {
    visibility: visible;
  }
}
</style>
