// import store from '@/store/modules/user';
// let Hashes = require('jshashes');
import { useAdminCommonStore } from '@/stores/adminCommon';
import { MD5, SHA1 } from 'jshashes';
const md5 = new MD5();
const Hash = new SHA1();
export const autographFun = (configData: any) => {
  const store = useAdminCommonStore();
  const config = configData;
  config.method = config.method.toUpperCase();
  let signData;
  const queryData = {
    app_guid: config.urlSuffix.app_guid,
    app_type: config.urlSuffix.app_type,
    token: config.urlSuffix.token,
  };
  const queryMd5 = md5.hex(JSON.stringify(queryData));
  if (config.method === 'POST') {
    const dataMd5 = config.data ? md5.hex(JSON.stringify(config.data)) : '';
    signData =
      config.method +
      '\n' +
      dataMd5 +
      '\n' +
      queryMd5 +
      '\n' +
      (config.data ? 'application/json' : 'text/plain') +
      '\n' +
      config.urlSuffix.expires +
      '\n' +
      config.urlSuffix.noncestr +
      '\n' +
      '/' +
      config.url.toLowerCase();
  } else {
    signData =
      config.method +
      '\n' +
      queryMd5 +
      '\n' +
      config.urlSuffix.expires +
      '\n' +
      config.urlSuffix.noncestr +
      '\n' +
      '/' +
      config.url.toLowerCase();
  }
  //签名key
  // let secretKey = store.state.secretKey;
  const secretKey = store.getSecret();
  return Hash.b64_hmac(secretKey, signData);
};
