<script setup lang="ts">
import { getUserMembers } from '@/api';
//分页
const total = ref(0);
const loading = ref(false);
const reqForm = reactive({
  merchantGuid: '',
  nickname: '',
  pageSize: 10,
  page: 1,
});
let goodsList = ref([]);
//获取产品列表
const getList = async () => {
  loading.value = true;
  let goodsRes = await getUserMembers(reqForm);
  loading.value = false;
  total.value = goodsRes.data.total;
  goodsList.value = goodsRes.data.data;
};
const handlePageChang = async (page) => {
  reqForm.page = page;
  getList();
};
//搜索
const onSearch = () => {
  getList();
};
getList();
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main v-loading="loading">
        <div class="hearder-box">
          <el-form :inline="true" :model="reqForm" class="demo-form-inline">
            <el-form-item label="用户昵称">
              <el-input v-model="reqForm.nickname" placeholder="用户昵称" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSearch">搜索</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table :data="goodsList" border style="width: 100%">
          <el-table-column prop="user.sysId" label="用户Id" width="80" />
          <el-table-column prop="user.nickname" label="用户昵称" width="180" />
          <el-table-column prop="user.mobile" label="手机号" width="120" />
          <el-table-column prop="cardTypeText" label="类型" width="120" />
          <el-table-column prop="createTime" label="购买时间" />
          <el-table-column prop="cardStartTime" label="开始时间" />
          <el-table-column prop="cardEndTime" label="结束时间" />
        </el-table>
      </el-main>
      <el-footer>
        <el-pagination background layout="prev,pager, next" :total="total" :current-page="reqForm.page"
          @current-change="handlePageChang" />
      </el-footer>
    </el-container>
  </div>
</template>

<style scoped lang="scss">
.add-point-box {
  display: flex;
  justify-content: center;
}
</style>
