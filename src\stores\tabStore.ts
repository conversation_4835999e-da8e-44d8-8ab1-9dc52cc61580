import { getUrlAfterParms, isEmptyObject } from '@/utils/common';
import { defineStore } from 'pinia';
interface TabType {
  path: string;
  title: string;
  state: boolean;
  fullPath: string;
  params: object;
}
export const useTabStore = defineStore('tabStore', {
  // 推荐使用 完整类型推断的箭头函数
  state: () => {
    return {
      // 所有这些属性都将自动推断其类型
      tabs: [
        {
          path: '/index',
          title: '首页',
          state: true,
          fullPath: '',
          params: {},
        },
      ] as TabType[],
    };
  },
  actions: {
    addTab(tab) {
      const isExist = this.tabs.findIndex((item) => {
        return item.path === tab.path;
      });
      const urlParams: object = getUrlAfterParms(tab.fullPath);
      if (isExist == -1) {
        if (!isEmptyObject(urlParams)) {
          tab.params = urlParams;
        }
        this.tabs.push(tab);
      } else {
        //如果路由相同 覆盖参数
        this.tabs[isExist].fullPath = tab.fullPath;
        this.tabs[isExist].params = urlParams;
      }
    },
    removeTab(path): any {
      const index = this.tabs.findIndex((item) => {
        return item.path === path;
      });
      if (index != -1) {
        this.tabs.splice(index, 1);
        const nextTab = this.tabs[index] || this.tabs[index - 1];
        return nextTab;
      } else {
        return {
          path: '/index',
          title: '首页',
          state: true,
          fullPath: '',
          params: {},
        };
      }
    },
    closeAll() {
      this.tabs = [
        {
          path: '/index',
          title: '首页',
          state: true,
          fullPath: '',
          params: {},
        },
      ];
    },
    closeOther(path) {
      const nowTabIndex = this.tabs.findIndex((item) => {
        return item.path === path;
      });
      type TabType = {
        path: string;
        title: string;
        state: boolean;
        fullPath: string;
        params: object;
      };
      let tab: TabType = {
        path: '',
        title: '',
        state: true,
        fullPath: '',
        params: {},
      };
      if (nowTabIndex != -1) {
        tab = this.tabs[nowTabIndex];
      }
      this.tabs.splice(1);
      this.tabs.push(tab);
    },
  },
});
