<script setup lang="ts">
import '@wangeditor/editor/dist/css/style.css';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import { getSystemConfigApi, saveConfigApi, uploadImg, getDefaultConfigApi } from '@/api';
import type { UploadInstance, UploadProps } from 'element-plus';
import { useAdminCommonStore } from '@/stores/adminCommon';
const store = useAdminCommonStore();

const editorRef = shallowRef();
const toolbarConfig = {
  excludeKeys: ['group-image', 'insertVideo', 'group-video'],
};
const handleCreated = (editor) => {
  editorRef.value = editor; // 记录 editor 实例，重要！
};
const reqForm = reactive({
  merchantGuid: '',
  configGroup: 'big_vip',
});
const reqSaveForm = reactive({
  merchantGuid: '',
  configKey: '',
  configValue: '',
  configGroup: 'big_vip',
});
const uploadImgRef = ref<UploadInstance>();
const beforeAvatarUpload: UploadProps['beforeUpload'] = (rawFile) => {
  let fileTypes = ['image/jpg', 'image/png', 'image/jpeg'];
  if (!fileTypes.includes(rawFile.type)) {
    ElMessage.warning('当前图片仅支持格式为：' + fileTypes.join(' ，'));
    return false;
  } else if (rawFile.size / 1024 / 1024 > 2) {
    ElMessage.error('图片必须小于2M');
    return false;
  }
  return true;
};
const upload = async (file, item) => {
  const formData = new FormData();
  // 添加要上传的文件名和文件对象,key就是你上传文件的字段名
  formData.append('img', file.file);
  let res = await uploadImg(formData);
  item.value = res.data;
};
const handleRemove = (item, index) => {
  item.value = '';
  uploadImgRef.value![index].clearFiles();
};
let merchantGuid = computed(() => {
  return store.getTenantInfo.guid;
});
const configList: any = ref([]);
const getSystemConfig = async () => {
  reqForm.merchantGuid = merchantGuid.value;
  let res = await getSystemConfigApi(reqForm);
  res.data.forEach((item) => {
    if (item.valueType === 'select') {
      item.value = item.value.split(',');
    }
  });
  configList.value = res.data;
};
const onSave = async (item) => {
  reqSaveForm.merchantGuid = reqForm.merchantGuid;
  reqSaveForm.configKey = item.key;
  if (item.valueType === 'select') {
    reqSaveForm.configValue = item.value.join();
  } else {
    reqSaveForm.configValue = item.value;
  }

  // reqSaveForm.configGroup = route.query.config as string;
  await saveConfigApi(reqSaveForm);
  ElMessage.success('保存成功');
  getSystemConfig();
};
const reqReSaveForm = reactive({
  merchantGuid: '',
  configKey: '',
});
const onReSave = async (item) => {
  reqReSaveForm.merchantGuid = reqForm.merchantGuid;
  reqReSaveForm.configKey = item.key;
  reqSaveForm.merchantGuid = reqForm.merchantGuid;
  // reqSaveForm.configGroup = route.query.config as string;
  reqSaveForm.configKey = item.key;
  let res = await getDefaultConfigApi(reqReSaveForm);
  if (res.data === '') {
    ElMessageBox.confirm('默认值为空是否保存', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(async () => {
        reqSaveForm.configValue = '';
        await saveConfigApi(reqSaveForm);
        ElMessage({
          type: 'success',
          message: '重置成功',
        });
        getSystemConfig();
      })
      .catch(() => {
        ElMessage({
          type: 'info',
          message: '取消重置',
        });
      });
  } else {
    reqSaveForm.configValue = res.data;
    await saveConfigApi(reqSaveForm);
    ElMessage.success('保存成功');
    getSystemConfig();
  }
};
// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (editor == null) return;
  editor.destroy();
});
getSystemConfig();
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main>
        <el-form ref="addForm" class="form-box" label-width="200px">
          <el-form-item :label="item.lable" v-for="(item, index) in configList" :key="index">
            <!-- <v-md-editor  v-model="item.value" height="300px"></v-md-editor> -->
            <div style="border: 1px solid #ccc" v-if="item.valueType === 'rich_text'">
              <Toolbar style="border-bottom: 1px solid #ccc" :defaultConfig="toolbarConfig" :editor="editorRef"
                mode="default" />
              <Editor style="height: 300px; overflow-y: hidden" v-model="item.value" mode="default"
                @onCreated="handleCreated" />
            </div>
            <div v-if="item.valueType === 'rich_text'" style="margin-bottom: 20px; width: 100%"></div>
            <el-input v-if="item.valueType === 'text'" class="input" v-model="item.value" :placeholder="item.lable" />
            <!-- :limit="1" " -->
            <el-upload ref="uploadImgRef" class="avatar-uploader" :before-upload="beforeAvatarUpload"
              :show-file-list="false" :http-request="(params) => upload(params, item)" v-if="item.valueType === 'img'">
              <div class="upload-img-box">
                <img v-if="item.value" :src="item.value" class="preview-img" />
                <div class="upload-btn" v-else>
                  <el-icon size="30" color="#cdd0d6"><i-ep-Plus /></el-icon>
                </div>
                <div class="operate-box" v-show="item.value">
                  <el-icon size="22" color="#ffffff" @click.stop="handleRemove(item, index)"><i-ep-Delete /></el-icon>
                </div>
              </div>
            </el-upload>
            <el-select v-if="item.valueType === 'select'" v-model="item.value" class="input" placeholder="Select"
              :multiple="true" size="large">
              <el-option v-for="i in item.options" :key="i.id" :label="i.name" :value="i.id" />
            </el-select>
            <el-radio-group v-if="item.valueType === 'radio'" v-model="item.value" class="input">
              <el-radio :label="i.id + ''" size="large" v-for="i in item.options" :key="i.id">{{ i.name }}</el-radio>
            </el-radio-group>
            <el-button type="primary" @click="onSave(item)">保存</el-button>
            <el-button type="primary" v-if="item.valueType === 'img' || item.valueType === 'text'"
              @click="onReSave(item)">恢复默认</el-button>
          </el-form-item>
        </el-form>
      </el-main>
    </el-container>
  </div>
</template>

<style scoped lang="scss">
.form-box {
  .input {
    width: 260px;
    margin-right: 18px;
  }
}

.avatar-uploader {
  margin-right: 20px;
}

.upload-img-box {
  width: 120px;
  height: 120px;
  border: 1px solid #cdd0d6;
  box-sizing: border-box;
  position: relative;
  border-radius: 6px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;

  .preview-img {
    width: 100%;
    height: 100%;
  }

  .operate-box {
    position: absolute;
    z-index: 9;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.5);
    visibility: hidden;
  }

  &:hover .operate-box {
    visibility: visible;
  }
}
</style>
