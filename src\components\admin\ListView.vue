<script setup lang="ts">
import { getAdminListApi, deleteAdminApi, createAdminApi, editAdminApi, getRoleListApi, bindMerchantRoleApi, getMerchantRolesApi } from '@/api';
import { useAdminCommonStore } from '@/stores/adminCommon';
import type { FormRules, FormInstance } from 'element-plus';
const store = useAdminCommonStore();
//分页
const total = ref(0);
const loading = ref(false);
let reqForm = reactive({
  adminType: 'merchant',
  merchantGuid: '',
  pageSize: 10,
  page: 1,
});
let goodsList = ref([]);
//弹窗相关
let dialogVisible = ref(false);
let dialogtype = ref('');
const addForm = ref<FormInstance>();
const addReqForm = reactive({
  guid: '',
  mobile: '',
  userName: '',
  nickname: '',
  password: '',
  merchantGuid: '',
  adminType: 'merchant',
});
const rules = reactive<FormRules>({
  mobile: [
    { required: true, message: '请输入电话', trigger: 'blur' },
    { pattern: /^1\d{10}$/, message: '手机号必须是11位数字', trigger: 'blur' },
  ],
  userName: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  nickname: [{ required: true, message: '请输入昵称', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
});
const logiLoading = ref(false);

let merchantGuid = computed(() => {
  return store.getTenantInfo.guid;
});
//获取管理员列表
const getList = async () => {
  loading.value = true;
  reqForm.merchantGuid = merchantGuid.value;
  let goodsRes = await getAdminListApi(reqForm);
  loading.value = false;
  total.value = goodsRes.data.total;
  goodsList.value = goodsRes.data.data;
};
const submitForm = async (formEl) => {
  formEl.validate(async (valid) => {
    if (!valid) {
      return;
    }
    logiLoading.value = true;
    try {
      if (dialogtype.value === 'add') {
        await createAdminApi(addReqForm);
        ElMessage.success('新增成功');
      } else if (dialogtype.value === 'edit') {
        await editAdminApi(addReqForm);
        ElMessage.success('修改成功');
      }
      dialogVisible.value = false;
      logiLoading.value = false;
      formEl.resetFields();
      getList();
    } catch (error: any) {
      ElMessage.error(error);
      logiLoading.value = false;
      throw new Error(error);
    }
  });
};

const handleDelete = async (item) => {
  try {
    let guid = item.guid;
    await deleteAdminApi({ guid });
    getList();
    ElMessage.success('删除成功');
  } catch (error: any) {
    ElMessage.success(error);
  }
};
const handlePageChang = async (page: number) => {
  reqForm.page = page;
  getList();
};
const onAddAdmin = () => {
  addReqForm.merchantGuid = merchantGuid.value;
  dialogVisible.value = true;
  dialogtype.value = 'add';
};
const onEdit = (item) => {
  addReqForm.guid = item.guid;
  addReqForm.mobile = item.mobile;
  addReqForm.password = item.password;
  addReqForm.userName = item.userName;
  addReqForm.nickname = item.nickname;
  dialogVisible.value = true;
  dialogtype.value = 'edit';
};
getList();
const roleList = ref<any[]>([])
const formRef = ref<FormInstance>()
const permissionVisible = ref(false)
// 获取角色列表
const getRoleList = async () => {
  try {
    const res = await getRoleListApi({
      merchantGuid: merchantGuid.value,
      pageSize: 100,
      page: 1
    })
    roleList.value = res.data.list
  } catch (error) {
    ElMessage.error('获取角色列表失败')
  }
}
getRoleList()

// 绑定角色
const onSetRole = (row: any) => {
  formData.adminUserId = row.sysId
  getMerchantRoles(row.sysId)
}

// 表单数据
const formData = reactive({
  merchantGuid: '',
  adminUserId: 0,
  merchantRoleIds: []
})

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await bindMerchantRoleApi({
          adminUserId: formData.adminUserId,
          merchantGuid: formData.merchantGuid,
          merchantRoleIds: formData.merchantRoleIds
        })
        ElMessage.success('设置成功')
        permissionVisible.value = false
        getList()
      } catch (error: any) {
        ElMessage.error(error.message || '设置失败')
      }
    }
  })
}
const roleDateilReq = reactive({
  merchantGuid: '',
  adminUserId: '',
})
const getMerchantRoles = async (userId) => {
  roleDateilReq.adminUserId = userId;
  let res = await getMerchantRolesApi(roleDateilReq)
  formData.merchantRoleIds = res.data.merchantRoleIds;
  permissionVisible.value = true;
}
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main v-loading="loading">
        <div class="header-box">
          <el-button type="primary" @click="onAddAdmin">新增管理员</el-button>
        </div>
        <el-table :data="goodsList" border style="width: 100%">
          <el-table-column prop="sysId" label="Id" width="80" />
          <el-table-column prop="nickname" label="管理昵称" width="180" />
          <el-table-column prop="userName" label="账户名称" width="180" />
          <el-table-column prop="roles" label="角色列表" width="280">
            <template #default="{ row }">
              <el-tag v-for="(item, index) in row.roles" :key="index" size="small" type="success"
                style="margin-right: 3px">
                {{ item.roleName }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button size="small" type="primary" @click="onEdit(scope.row)">编辑</el-button>
              <el-button size="small" type="warning" @click="onSetRole(scope.row)">绑定角色</el-button>
              <el-popconfirm confirm-button-text="确认" cancel-button-text="取消" icon-color="red" title="是否删除该管理?"
                @confirm="handleDelete(scope.row)">
                <template #reference>
                  <el-button size="small" type="danger">删除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </el-main>
      <el-footer>
        <el-pagination background layout="prev,pager, next" :total="total" :current-page="reqForm.page"
          @current-change="handlePageChang" />
      </el-footer>
    </el-container>
    <el-dialog v-model="dialogVisible" title="新增/修改 管理员" width="600px">
      <el-form ref="addForm" :model="addReqForm" :rules="rules">
        <el-form-item label="电话" prop="mobile">
          <el-input v-model="addReqForm.mobile" autocomplete="off" placeholder="电话" />
        </el-form-item>
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="addReqForm.nickname" autocomplete="off" placeholder="昵称" />
        </el-form-item>
        <el-form-item label="用户" prop="userName">
          <el-input v-model="addReqForm.userName" autocomplete="off" placeholder="用户" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input type="password" v-model="addReqForm.password" autocomplete="off" placeholder="密码" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitForm(addForm)" :loading="logiLoading">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-dialog title="权限设置" v-model="permissionVisible" width="700px">
      <el-form ref="formRef" :model="formData" label-width="120px">
        <el-form-item label="角色选择" prop="merchantRoleIds">
          <el-select v-model="formData.merchantRoleIds" multiple placeholder="请选择角色" style="width: 100%"> <el-option
              v-for="item in roleList" :key="item.sysId" :label="item.roleName" :value="item.sysId" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="permissionVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.header-box {
  margin-bottom: 18px;
}
</style>
