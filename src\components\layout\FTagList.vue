<template>
  <div class="f-tab">
    <el-tabs
      class="tab-box"
      v-model="activeTab"
      type="card"
      @tab-remove="handleRemoveTab"
      @tab-click="handleTab"
      @tab-change="handleChange">
      <el-tab-pane
        :closable="item.path != '/index'"
        v-for="item in tabList"
        :key="item.path"
        :label="item.title"
        :name="item.path">
        <!-- {{ item.path }} -->
      </el-tab-pane>
    </el-tabs>
    <el-dropdown>
      <span class="el-dropdown-link">
        <el-icon>
          <i-ep-arrow-down />
        </el-icon>
      </span>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item @click="handleCloseOther">关闭其他</el-dropdown-item>
          <el-dropdown-item @click="handleCloseAll">全部关闭</el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>
<script setup lang="ts">
import { useRoute, useRouter, onBeforeRouteUpdate } from 'vue-router';
import { useTabStore } from '@/stores/tabStore';
import { getUrlAfterParms, isEmptyObject } from '@/utils/common';
const route = useRoute();
const router = useRouter();
const store = useTabStore();
// const activeTab = ref(route.path);
const activeTab = ref(route.path);
let tabList: any = computed(() => {
  return store.tabs;
});
// console.log(tabList, 'tabList------------tabList', store.tabs);
// let tabList = store.tabs;
onBeforeRouteUpdate((to, from) => {
  const tab = {
    path: to.path,
    fullPath: to.fullPath,
    title: to.meta.title,
    state: true,
    params: {},
  };
  // if (!isEmptyObject(urlParams)) {
  //   tab.params = urlParams;
  // }
  store.addTab(tab);
  activeTab.value = to.path;
});

onBeforeMount(() => {
  let path = route.path;
  let isNowPath = tabList.value.find((item) => {
    return item.path === path;
  });
  if (!isNowPath) {
    router.push({
      path: route.path,
      query: route.query,
    });
    let tab = {
      path: route.path,
      fullPath: route.fullPath,
      state: true,
      params: route.query,
      title: route.meta.title,
    };
    store.addTab(tab);
  }
  // tab.path = route.path;
  // tab.fullPath = route.fullPath;
  // 刷新后重定向路由
});

function handleRemoveTab(pane: any) {
  const nextTab = store.removeTab(pane);
  const params = nextTab.params;
  if (isEmptyObject(params)) {
    router.push({
      path: nextTab.path,
    });
  } else {
    router.push({
      path: nextTab.path,
      query: params,
    });
  }
  if (activeTab.value === pane) {
    activeTab.value = nextTab.path;
  }
}
function handleChange(pane) {
  activeTab.value = pane;
}
function handleTab(pane) {
  let path = tabList.value[pane.index].path;
  let params = tabList.value[pane.index].params;
  if (isEmptyObject(params)) {
    router.push({
      path: path,
    });
  } else {
    router.push({
      path: path,
      query: params,
    });
  }
}
function handleCloseOther() {
  store.closeOther(activeTab.value);
  tabList.value = store.tabs;
}
function handleCloseAll() {
  store.closeAll();
  tabList.value = store.tabs;
  activeTab.value = store.tabs[0].path;
}
</script>
<style scoped lang="scss">
.f-tab {
  display: flex;
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
}

.f-tab .tab-box {
  /* @apply flex-1; */
  flex: 1;
  overflow-x: hidden;
}

:deep(.el-tabs__header) {
  border-bottom: none;
  margin-bottom: 0px;
}

:deep(.el-tabs__nav) {
  border: none !important;
}

:deep(.el-tabs__item) {
  background-color: #ffffff;
  margin-right: 10px;
  border-radius: 5px;
  height: 32px;
  line-height: 32px;
}

:deep(.el-tabs__nav-next),
:deep(.el-tabs__nav-prev) {
  height: 32px;
  line-height: 32px;
}

/* :deep(.el-tabs__new-tab) {
  display: none !important;
} */

.el-dropdown-link {
  width: 42px;
  height: 32px;
  background-color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 5px;
  &:focus {
    outline: none;
  }
}
</style>
