<template>
  <div>
    <el-container class="wrapper">
      <el-main v-loading="loading">
        <div class="header-box">
          <el-form :inline="true" :model="reqForm" class="demo-form-inline">
            <el-form-item label="订单编号">
              <el-input v-model="reqForm.orderNo" placeholder="请输入订单编号" clearable />
            </el-form-item>
            <el-form-item label="订单状态">
              <div class="mode-box">
                <div :class="['item', { active: modeStatus === item.value }]" v-for="(item, index) in orderStatusList"
                  :key="index" @click="onChangeOrderStatus(item.value)">
                  {{ item.name }}
                </div>
              </div>
            </el-form-item>
            <el-form-item label="智能体名称">
              <el-input v-model="reqForm.agentName" placeholder="请输入智能体名称" clearable />
            </el-form-item>
            <el-form-item label="时间范围">
              <el-date-picker v-model="dateRange" type="datetimerange" range-separator="至" start-placeholder="开始时间"
                end-placeholder="结束时间" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss"
                @change="onDateRangeChange" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSearch">搜索</el-button>
              <el-button @click="onReset">重置</el-button>
              <el-button type="success" @click="openAddDialog">新增会员</el-button>
            </el-form-item>
          </el-form>
        </div>

        <el-table :data="orderList" border style="width: 100%">
          <el-table-column prop="sysId" label="订单ID" width="80" />
          <el-table-column label="智能体信息" width="200">
            <template #default="scope">
              <div class="agent-info">
                <el-avatar :src="scope.row.agent.agentAvatar" :size="30" />
                <div class="agent-details">
                  <div class="agent-name">{{ scope.row.agent.agentName }}</div>
                  <div class="agent-price">¥{{ (scope.row.agent.price / 100).toFixed(2) }}</div>
                  <el-tag v-if="scope.row.agent.isFeatured === 1" type="warning" size="small">精品</el-tag>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="购买用户" width="180">
            <template #default="scope">
              <div class="user-info">
                <el-avatar :src="scope.row.buyer.headImgurl" :size="30" />
                <div class="user-details">
                  <div>{{ scope.row.buyer.nickname }}</div>
                  <div class="mobile">{{ scope.row.buyer.mobile }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="智能体创作者" width="150">
            <template #default="scope">
              <div class="creator-info">
                <el-avatar :src="scope.row.agentOwner.headImgurl" :size="24" />
                <span class="creator-name">{{ scope.row.agentOwner.nickname }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="orderNo" label="订单编号" min-width="180" />
          <el-table-column label="金额信息" width="150">
            <template #default="scope">
              <div class="amount-info">
                <div class="pay-amount">{{ scope.row.payAmountText }}</div>
                <div class="platform-fee">平台费: {{ scope.row.platformFeeText }}</div>
                <div class="creator-income">创作者收入: {{ scope.row.creatorIncomeText }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="orderStatusText" label="订单状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusTagType(scope.row.orderStatus)" size="small">
                {{ scope.row.orderStatusText }}
              </el-tag>
            </template>
          </el-table-column>
          <!-- <el-table-column label="邀请人" width="150">
            <template #default="scope">
              <div v-if="scope.row.inviter" class="inviter-info">
                <el-avatar :src="scope.row.inviter.headImgurl" :size="24" />
                <span class="inviter-name">{{ scope.row.inviter.nickname }}</span>
                <div class="commission">{{ scope.row.inviterCommissionText }}</div>
              </div>
              <span v-else class="no-inviter">无</span>
            </template>
          </el-table-column> -->
          <el-table-column prop="createTime" label="下单时间" width="160" />
          <el-table-column prop="payTime" label="支付时间" width="160" />
          <el-table-column prop="expireTime" label="到期时间" width="160" />
          <!-- <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <el-button size="small" type="primary" @click="viewDetail(scope.row)">
                查看详情
              </el-button>
            </template>
          </el-table-column> -->
        </el-table>
      </el-main>

      <el-footer>
        <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="total"
          :current-page="reqForm.page" :page-size="reqForm.limit" :page-sizes="[10, 20, 50, 100]"
          @current-change="handlePageChange" @size-change="handleSizeChange" />
      </el-footer>
    </el-container>


    <!-- 新增精品智能体订单弹窗 -->
    <el-dialog v-model="addDialogVisible" title="新增精品智能体会员" width="600px" :before-close="handleAddDialogClose">
      <el-form :model="addForm" :rules="addFormRules" ref="addFormRef" label-width="120px">
        <el-form-item label="精品智能体" prop="agentGuid" required>
          <el-select v-model="addForm.agentGuid" placeholder="请选择精品智能体" style="width: 100%" @change="onAgentChange">
            <el-option v-for="agent in agentList" :key="agent.guid"
              :label="`${agent.agentName} - ¥${(agent.price / 100).toFixed(2)}`" :value="agent.guid">
              <div class="agent-option">
                <span class="agent-name">{{ agent.agentName }}</span>
                <span class="agent-price">¥{{ (agent.price / 100).toFixed(2) }}</span>
                <el-tag v-if="agent.isFeatured === 1" type="warning" size="small">精品</el-tag>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="用户选择" required>
          <el-radio-group v-model="userSelectType" @change="onUserSelectTypeChange">
            <el-radio label="userGuid">用户GUID</el-radio>
            <el-radio label="nickname">用户昵称</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="userSelectType === 'userGuid'" label="用户GUID" prop="userGuid">
          <el-input v-model="addForm.userGuid" placeholder="请输入用户GUID" clearable />
        </el-form-item>

        <el-form-item v-if="userSelectType === 'nickname'" label="用户昵称" prop="nickname">
          <el-input v-model="addForm.nickname" placeholder="请输入用户昵称" clearable />
        </el-form-item>

        <el-form-item label="实际支付金额" prop="actualPayAmount">
          <el-input-number v-model="addForm.actualPayAmount" :min="0" :precision="2" style="width: 100%" />
        </el-form-item>

        <el-form-item v-if="selectedAgent" label="智能体信息">
          <div class="agent-preview">
            <div class="agent-detail">
              <div><strong>智能体名称：</strong>{{ selectedAgent.agentName }}</div>
              <div><strong>智能体价格：</strong>¥{{ (selectedAgent.price / 100).toFixed(2) }}</div>
              <div><strong>是否精品：</strong>{{ selectedAgent.isFeatured === 1 ? '是' : '否' }}</div>
              <div><strong>创作者：</strong>{{ selectedAgent.creator?.nickname || '未知' }}</div>
            </div>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleAddDialogClose">取消</el-button>
          <el-button type="primary" @click="handleAddMember" :loading="addLoading">确认添加</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { getPayAgentpBoutinqueOrderApi, manualAddBoutiqueOrderApi, getAiAgentListApi } from '@/api';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';

// 响应式数据
const loading = ref(false);
const total = ref(0);
const modeStatus = ref('');
const dateRange = ref<[string, string] | null>(null);
const detailDialogVisible = ref(false);
const currentOrder = ref<any>(null);

// 新增会员相关数据
const addDialogVisible = ref(false);
const addLoading = ref(false);
const addFormRef = ref<FormInstance>();
const userSelectType = ref('userGuid');
const selectedAgent = ref<any>(null);

// 新增会员表单数据
const addForm = reactive({
  merchantGuid: '', // 商户uuid，后端会自动填充
  agentGuid: '', // 套餐guid
  userGuid: '', // 用户guid
  nickname: '', // 用户昵称
  actualPayAmount: 0// 实际支付金额
});

// 表单验证规则
const addFormRules: FormRules = {
  packageGuid: [
    { required: true, message: '请选择套餐', trigger: 'change' }
  ],
  userGuid: [
    {
      required: true,
      message: '请输入用户GUID',
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (userSelectType.value === 'userGuid' && !value) {
          callback(new Error('请输入用户GUID'));
        } else {
          callback();
        }
      }
    }
  ],
  nickname: [
    {
      required: true,
      message: '请输入用户昵称',
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (userSelectType.value === 'nickname' && !value) {
          callback(new Error('请输入用户昵称'));
        } else {
          callback();
        }
      }
    }
  ]
};

// 订单状态列表
const orderStatusList = reactive([
  { name: '全部', value: '' },
  { name: '待支付', value: 100 },
  { name: '已支付', value: 200 },
  { name: '取消支付', value: 300 },
  { name: '支付超时', value: 400 },
  { name: '已退款', value: 500 },
]);

// 请求参数
const reqForm = reactive({
  merchantGuid: '', // 商户uuid，后端会自动填充
  orderStatus: '' as string | number, // 订单状态
  agentName: '', // 智能体名称
  orderNo: '', // 订单编号
  userMobile: '', // 用户手机号
  startTime: '', // 开始时间
  endTime: '', // 结束时间
  page: 1, // 页数
  limit: 20 // 每页条数
});

// 订单列表
const orderList = ref<any[]>([]);

// 获取订单状态标签类型
const getStatusTagType = (status: number) => {
  switch (status) {
    case 100: return 'warning'; // 待支付
    case 200: return 'success'; // 已支付
    case 300: return 'info'; // 取消支付
    case 400: return 'danger'; // 支付超时
    case 500: return 'primary'; // 已退款
    default: return 'info';
  }
};

// 切换订单状态
const onChangeOrderStatus = (value: string | number) => {
  modeStatus.value = value.toString();
  reqForm.orderStatus = value;
  reqForm.page = 1;
  getList();
};

// 时间范围变化
const onDateRangeChange = (dates: [string, string] | null) => {
  if (dates) {
    reqForm.startTime = dates[0];
    reqForm.endTime = dates[1];
  } else {
    reqForm.startTime = '';
    reqForm.endTime = '';
  }
};

// 获取订单列表
const getList = async () => {
  try {
    loading.value = true;
    const response = await getPayAgentpBoutinqueOrderApi(reqForm);

    if (response.data) {
      total.value = response.data.total || 0;
      orderList.value = response.data.data || [];
    }
  } catch (error) {
    console.error('获取订单列表失败:', error);
    ElMessage.error('获取订单列表失败');
    orderList.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 搜索
const onSearch = () => {
  reqForm.page = 1;
  getList();
};

// 重置
const onReset = () => {
  Object.assign(reqForm, {
    orderStatus: '',
    agentName: '',
    orderNo: '',
    userMobile: '',
    startTime: '',
    endTime: '',
    page: 1,
    limit: 20
  });
  modeStatus.value = '';
  dateRange.value = null;
  getList();
};

// 分页变化
const handlePageChange = (page: number) => {
  reqForm.page = page;
  getList();
};

// 每页条数变化
const handleSizeChange = (size: number) => {
  reqForm.limit = size;
  reqForm.page = 1;
  getList();
};

// 查看详情
const viewDetail = (order: any) => {
  currentOrder.value = order;
  detailDialogVisible.value = true;
};

// 关闭详情弹窗
const handleDetailClose = () => {
  detailDialogVisible.value = false;
  currentOrder.value = null;
};

// 获取套餐类型文本
const getPackageTypeText = (type: number) => {
  switch (type) {
    case 1: return '月卡';
    case 2: return '季卡';
    case 3: return '年卡';
    case 4: return '自定义';
    default: return '未知';
  }
};
// 搜索筛选参数
const searchParams = reactive({
  merchantGuid: '',
  categoryGuid: '',
  agentType: '',
  auditStatus: '',
  status: '',
  isPaid: '',
  agentName: '',
  creatorNickname: '',
  pageSize: 300,
  page: 1,
});
// 数据类型定义
interface AgentItem {
  categoryGuid: string;
  agentType: number;
  auditStatus: number;
  status: number;
  isPaid: number;
  agentName: string;
  creatorNickname: string;
  [key: string]: any;
}

// 列表数据
let agentList = ref<AgentItem[]>([]);
// 获取智能体列表
const getAgentListList = async () => {
  loading.value = true;
  try {
    let res = await getAiAgentListApi(searchParams);
    if (res.data && res.data.data) {
      agentList.value = res.data.data.filter(item => {
        return item.isFeatured === 1;
      });
      total.value = res.data.total;
    }
  } catch (error) {
    ElMessage.error('获取智能体列表失败');
  } finally {
    loading.value = false;
  }
};

// 打开新增会员弹窗
const openAddDialog = () => {
  addDialogVisible.value = true;
  getAgentListList();
  resetAddForm();
};

// 重置新增表单
const resetAddForm = () => {
  Object.assign(addForm, {
    merchantGuid: '',
    agentGuid: '',
    userGuid: '',
    nickname: '',
    actualPayAmount: 0
  });
  userSelectType.value = 'userGuid';
  selectedAgent.value = null;
  addFormRef.value?.clearValidate();
};

// 智能体选择变化
const onAgentChange = (agentGuid: string) => {
  selectedAgent.value = agentList.value.find(agent => agent.guid === agentGuid) || null;
};

// 用户选择类型变化
const onUserSelectTypeChange = (type: string) => {
  // 清空另一个字段的值
  if (type === 'userGuid') {
    addForm.nickname = '';
  } else {
    addForm.userGuid = '';
  }
  addFormRef.value?.clearValidate();
};

// 关闭新增弹窗
const handleAddDialogClose = () => {
  addDialogVisible.value = false;
  resetAddForm();
};

// 添加会员
const handleAddMember = async () => {
  if (!addFormRef.value) return;

  try {
    await addFormRef.value.validate();

    addLoading.value = true;

    // 构建请求参数
    const params: any = {
      agentGuid: addForm.agentGuid,
      actualPayAmount: addForm.actualPayAmount,
    };

    // 根据选择类型添加用户信息
    if (userSelectType.value === 'userGuid') {
      params.userGuid = addForm.userGuid;
    } else {
      params.nickname = addForm.nickname;
    }

    const { data } = await manualAddBoutiqueOrderApi(params);
    if (data.data) {
      handleAddDialogClose();
      getList(); // 刷新列表
    } else {
      ElMessage.warning(data.message);
    }

  } catch (error: any) {
    console.error('添加会员失败:', error);

  } finally {
    addLoading.value = false;
  }
};

// 初始化加载数据
onMounted(() => {
  getList();
});
</script>

<style scoped lang="scss">
.wrapper {
  height: 100%;

  .header-box {
    background: #f5f7fa;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
  }
}

.mode-box {
  background-color: #eee;
  display: flex;
  padding: 4px;
  border-radius: 6px;
  width: 400px;

  .item {
    background-color: #fff;
    margin: 0 2px;
    flex: 1;
    border-radius: 4px;
    text-align: center;
    cursor: pointer;
    height: 28px;
    line-height: 28px;
    font-size: 12px;
    transition: all 0.3s;

    &:hover {
      background-color: #f0f9ff;
    }

    &.active {
      background-color: #409eff;
      color: #fff;
    }
  }
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;

  .user-details {
    .mobile {
      font-size: 12px;
      color: #666;
    }
  }
}

.package-info {
  .package-name {
    font-weight: 500;
    margin-bottom: 4px;
  }

  .package-type,
  .duration {
    font-size: 12px;
    color: #666;
  }
}

.amount-info {
  .pay-amount {
    color: #f56c6c;
    font-weight: 500;
    margin-bottom: 4px;
  }

  .original-amount {
    font-size: 12px;
    color: #999;
    text-decoration: line-through;
  }
}

.inviter-info {
  display: flex;
  align-items: center;
  gap: 6px;

  .inviter-name {
    font-size: 12px;
  }
}

.no-inviter {
  color: #999;
  font-size: 12px;
}

.commission-info {
  font-size: 12px;

  div {
    margin-bottom: 2px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.order-detail {
  .el-descriptions {
    margin-top: 20px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

// 新增会员弹窗样式
.agent-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  gap: 8px;

  .agent-name {
    font-weight: 500;
    flex: 1;
  }

  .agent-price {
    color: #f56c6c;
    font-weight: 600;
  }
}

.agent-preview {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 16px;

  .agent-detail {
    div {
      margin-bottom: 8px;
      line-height: 1.5;

      &:last-child {
        margin-bottom: 0;
      }

      strong {
        color: #333;
        margin-right: 8px;
      }
    }
  }
}

.agent-info {
  display: flex;
  align-items: center;
  gap: 8px;

  .agent-details {
    .agent-name {
      font-weight: 500;
      margin-bottom: 4px;
    }

    .agent-price {
      font-size: 12px;
      color: #f56c6c;
      font-weight: 600;
    }
  }
}

.creator-info {
  display: flex;
  align-items: center;
  gap: 6px;

  .creator-name {
    font-size: 12px;
  }
}
</style>