<script setup lang="ts">
import { deleteKnowLedgeApi, createKnowLedgeApi, getKnowLedgeListApi, updateKnowLedgeApi } from '@/api';
import type { FormInstance, FormRules } from 'element-plus';
import { useRouter } from 'vue-router';
const router = useRouter();
const loading = ref(false);
const knowLedgeReq = reactive({
  merchantGuid: '',
  pageSize: 10,
  page: 1,
});
const knowLedgeList = ref([]);
const total = ref(0);
const getKnowLedgeList = async () => {
  let res = await getKnowLedgeListApi(knowLedgeReq);
  knowLedgeList.value = res.data.data;
  total.value = res.data.total;
};
getKnowLedgeList();
// const getTenantList = async () => {
//   let res = await getTenantListApi();
//   tenantList.value = res.data;
//   knowLedgeReq.merchantGuid = res.data[0].guid;
//   getKnowLedgeList();
// };
// getTenantList();
let createKnowledge = reactive({
  merchantGuid: '',
  knowledgeTitle: '',
});
const dialogConfig = reactive({
  dialogtype: 'ADD',
  isShow: false,
});
const rules = reactive<FormRules>({
  knowledgeTitle: [{ required: true, message: '请输入知识库名称', trigger: 'change' }],
});
const onAddScene = () => {
  dialogConfig.isShow = true;
  dialogConfig.dialogtype = 'ADD';
};
const addForm = ref<FormInstance>();
const onSubmit = async (formEl) => {
  formEl.validate(async (valid) => {
    if (!valid) {
      return;
    }
    try {
      if (dialogConfig.dialogtype === 'ADD') {
        await createKnowLedgeApi(createKnowledge);
        ElMessage.success('新增成功');
      } else if (dialogConfig.dialogtype === 'EDIT') {
        await updateKnowLedgeApi(createKnowledge);
        ElMessage.success('修改成功');
      }
      nextTick(() => {
        formEl.resetFields();
        dialogConfig.isShow = false;
      });
      getKnowLedgeList();
    } catch (error: any) {
      ElMessage.error(error);
      throw new Error(error);
    }
  });
};
const onEdit = (row) => {
  createKnowledge = Object.assign(createKnowledge, row);
  dialogConfig.dialogtype = 'EDIT';
  dialogConfig.isShow = true;
};
const handlePageChang = (page) => {
  knowLedgeReq.page = page;
  getKnowLedgeList();
};
const onLook = (row) => {
  router.push({
    name: 'knowledgedetail',
    query: {
      guid: row.guid,
    },
  });
};
const onTest = (row) => {
  router.push({
    name: 'KnowledgeTestView',
    query: {
      guid: row.guid,
    },
  });
};
const handleDelete = async (item) => {
  await deleteKnowLedgeApi({ guid: item.guid });
  ElMessage.success('删除成功');
  getKnowLedgeList();
};
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main>
        <div class="header-box">
          <el-form :inline="true" class="demo-form-inline">
            <el-form-item><el-button type="primary" @click="onAddScene">新增配置</el-button></el-form-item>
          </el-form>
        </div>
        <el-table v-loading="loading" :data="knowLedgeList" border style="width: 100%">
          <el-table-column prop="sysId" label="id" width="80" />
          <el-table-column prop="knowledgeTitle" label="知识库名称" />
          <el-table-column label="操作" width="420">
            <template #default="scope">
              <el-button size="small" type="primary" @click="onLook(scope.row)">知识库文档</el-button>
              <el-button size="small" type="primary" @click="onTest(scope.row)">知识库召回测试</el-button>
              <el-button size="small" type="primary" @click="onEdit(scope.row)">编辑</el-button>
              <el-popconfirm confirm-button-text="确认" cancel-button-text="取消" icon-color="red" title="是否删除该知识库?"
                @confirm="handleDelete(scope.row)">
                <template #reference>
                  <el-button size="small" type="danger">删除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </el-main>
      <el-footer>
        <el-pagination background layout="prev,pager, next" :total="total" :current-page="knowLedgeReq.page"
          @current-change="handlePageChang" />
      </el-footer>
    </el-container>
    <el-dialog v-model="dialogConfig.isShow" title="创建/修改 知识库" width="600" draggable>
      <el-form ref="addForm" :model="createKnowledge" class="demo-form-inline" :rules="rules" label-width="100px">
        <el-form-item label="知识库名称" prop="knowledgeTitle">
          <el-input v-model="createKnowledge.knowledgeTitle" placeholder="请输入知识库名称"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit(addForm)">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss"></style>
