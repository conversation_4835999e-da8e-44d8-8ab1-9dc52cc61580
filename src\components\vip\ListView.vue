<script setup lang="ts">
import { getVipListApi, getTenantListApi, addChatCountApi } from '@/api';
//分页
const total = ref(0);
const loading = ref(false);
let pointEditVisible = ref(false);
const reqForm = reactive({
  merchantGuid: '',
  nickname: '',
  parentUid: '',
  mobile: '',
  pageSize: 10,
  page: 1,
});
const addPointReq = reactive({
  guid: '',
  addCount: 1,
});
let goodsList = ref([]);
let tenantList: any = ref([]);
//获取产品列表
const getList = async () => {
  loading.value = true;
  let goodsRes = await getVipListApi(reqForm);
  loading.value = false;
  total.value = goodsRes.data.total;
  goodsList.value = goodsRes.data.data;
};
//获取商户列表
const getTenantList = async () => {
  let res = await getTenantListApi();
  tenantList.value = res.data;
};
const handlePageChang = async (page) => {
  reqForm.page = page;
  getList();
};
//搜索
const onSearch = () => {
  getList();
};
const onPointEdit = (item) => {
  pointEditVisible.value = true;
  addPointReq.guid = item.guid;
};
const onAddPointSave = async () => {
  await addChatCountApi(addPointReq);
  ElMessage.success('增加成功');
  pointEditVisible.value = false;
  getList();
};
getList();
getTenantList();
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main v-loading="loading">
        <div class="hearder-box">
          <el-form :inline="true" :model="reqForm" class="demo-form-inline">
            <el-form-item label="手机号码">
              <el-input v-model="reqForm.mobile" placeholder="手机号码" />
            </el-form-item>
            <el-form-item label="用户昵称">
              <el-input v-model="reqForm.nickname" placeholder="用户昵称" />
            </el-form-item>
            <el-form-item label="上级用户Id">
              <el-input v-model="reqForm.parentUid" placeholder="用户Id" />
            </el-form-item>
            <!-- <el-form-item label="所属商家">
              <el-select v-model="reqForm.merchantGuid" placeholder="请选择">
                <el-option label="无" value="" />
                <el-option :label="item.merchantName" :value="item.guid" v-for="item in tenantList" :key="item.guid" />
              </el-select>
            </el-form-item> -->
            <el-form-item>
              <el-button type="primary" @click="onSearch">搜索</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table :data="goodsList" border style="width: 100%">
          <el-table-column prop="sysId" label="用户Id" width="80" />
          <el-table-column prop="guid" label="guid" width="280" />
          <el-table-column prop="nickname" label="用户昵称" width="180" />
          <el-table-column prop="headImgurl" label="用户头像" width="120">
            <template #default="scope">
              <el-image :src="scope.row.headImgurl" style="width: 50px; height: 50px; border-radius: 50%"></el-image>
            </template>
          </el-table-column>
          <el-table-column prop="mobile" label="手机号码" width="120" />
          <el-table-column prop="statusText" label="绑定公众号" width="100">
            <template #default="scope">
              {{ scope.row.gzhOpenid ? '已绑定' : '未绑定' }}
            </template>
          </el-table-column>
          <el-table-column prop="userAssets.chatCount" label="剩余聊天点数">
            <template #default="scope">
              <span style="margin-right: 20px">{{ scope.row.userAssets.chatCount }}</span>
              <el-button size="small" type="primary" @click="onPointEdit(scope.row)">增加</el-button>
            </template>
          </el-table-column>
          <el-table-column prop="parentUser.nickname" label="上级用户" />
          <el-table-column prop="createTime" label="注册时间" />
          <el-table-column prop="loginTime" label="最后活跃时间" />
        </el-table>
      </el-main>
      <el-footer>
        <el-pagination background layout="prev,pager, next" :total="total" :current-page="reqForm.page"
          @current-change="handlePageChang" />
      </el-footer>
    </el-container>
    <el-dialog v-model="pointEditVisible" title="增加点数" width="20%" center>
      <div class="add-point-box">
        <el-input-number v-model="addPointReq.addCount" :min="1" />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="pointEditVisible = false">取消</el-button>
          <el-button type="primary" @click="onAddPointSave"> 确认 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.add-point-box {
  display: flex;
  justify-content: center;
}
</style>
