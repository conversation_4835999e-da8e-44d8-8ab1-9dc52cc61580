import{d as ae,r as c,a as _,c as f,b as e,w as n,h as a,bW as le,e as oe,C as ne,u as re,bX as se,o as u,T as q,I as w,f as k,G as C,H as x,i as y,U as de,ah as ie,ai as ue,E as h,J as pe,bY as ge,bZ as me,ax as ce,m as _e,n as fe,l as ve,p as Ve,q as be,s as we,v as ye,y as he,x as Se,Y as Ae,O as ke,P as Ce,k as xe,t as Ee,Z as Te,_ as Ue}from"./index-6e8b0ade.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                  *//* empty css                 *//* empty css                  *//* empty css                    */import{_ as De,a as Ie}from"./plus-19e9063c.js";/* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                     */const Le={class:"hearder-box"},qe={class:"upload-img-box"},Fe=["src"],Pe={key:1,class:"upload-btn"},Ne={class:"operate-box"},Oe=ae({__name:"ListView",setup(Me){re();const E=c(0),S=c(!1),m=_({merchantGuid:"",agentVendorSign:"coze",agentType:"text",pageSize:10,page:1});let V=_([]),F=_([{name:"文本",value:"text"}]),T=c([]);c([]);const b=async()=>{S.value=!0;let s=await se(m);S.value=!1,E.value=s.data.total,T.value=s.data.data},P=async()=>{let s=await le();V.push(...s.data),m.agentVendorSign=V[0].sign,b()},N=async s=>{m.page=s,b()},O=s=>{o=Object.assign(o,s),p.isShow=!0,p.dialogtype="EDIT"},M=_({agentVendorSign:[{required:!0,message:"请输入厂商标识",trigger:"change"}],agentName:[{required:!0,message:"请输入智能体名称",trigger:"change"}],agentSecretToken:[{required:!0,message:"请输入智能体令牌",trigger:"change"}],agentCover:[{required:!0,message:"请上传封面图",trigger:"change"}],agentType:[{required:!0,message:"请选择模型类型",trigger:"change"}],agentDesc:[{required:!0,message:"请输入厂商标识",trigger:"change"}],agentStatus:[{required:!0,message:"请选择模型状态",trigger:"change"}],isMemberFree:[{required:!0,message:"请选择是否支持免费",trigger:"change"}],usePrice:[{required:!0,message:"请输入AI算力点数价格",trigger:"change"}],showOrder:[{required:!0,message:"请输入展示顺序",trigger:"change"}]}),U=c();let o=_({guid:"",merchantGuid:"",agentVendorSign:"",agentName:"",agentSign:"",agentSecretToken:"",agentType:"",agentDesc:"",agentStatus:1,isMemberFree:1,agentCover:"",usePrice:"1",showOrder:"1",deployAddress:"",deployAddressSecret:""});const p=_({dialogtype:"ADD",isShow:!1}),R=c(),j=s=>{let l=["image/jpg","image/png","image/jpeg"];return new Promise((d,v)=>{const r=new Image;if(r.src=URL.createObjectURL(s),!l.includes(s.type)){h.warning("当前图片仅支持格式为："+l.join(" ，")),v(!1);return}d(!0)})},z=async s=>{const l=new FormData;l.append("img",s.file);let d=await pe(l);o.agentCover=d.data},B=()=>{o.agentCover=""},G=()=>{p.isShow=!0,p.dialogtype="ADD"},Y=async s=>{s.validate(async l=>{if(l)try{p.dialogtype==="ADD"?(await ge(o),h.success("新增成功")):p.dialogtype==="EDIT"&&(await me(o),h.success("修改成功")),ce(()=>{s.resetFields(),p.isShow=!1}),b()}catch(d){throw h.error(d),new Error(d)}})};return P(),(s,l)=>{const d=_e,v=fe,r=ve,A=Ve,D=be,i=we,Z=ye,$=he,H=Se,J=Ae,W=oe,X=De,I=ke,K=Ie,Q=Ce,g=xe,L=Ee,ee=ne,te=Te;return u(),f("div",null,[e(W,{class:"wrapper"},{default:n(()=>[q((u(),w($,null,{default:n(()=>[k("div",Le,[e(D,{inline:!0,model:a(m),class:"demo-form-inline"},{default:n(()=>[e(r,{label:"所属厂商"},{default:n(()=>[e(v,{modelValue:a(m).agentVendorSign,"onUpdate:modelValue":l[0]||(l[0]=t=>a(m).agentVendorSign=t),placeholder:"请选择",onChange:b},{default:n(()=>[(u(!0),f(C,null,x(a(V),t=>(u(),w(d,{label:t.name,value:t.sign,key:t.sign},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,null,{default:n(()=>[e(A,{type:"primary",onClick:G},{default:n(()=>[y("新增")]),_:1})]),_:1})]),_:1},8,["model"])]),e(Z,{data:a(T),border:"",style:{width:"100%"}},{default:n(()=>[e(i,{prop:"sysId",label:"sysId",width:"80"}),e(i,{prop:"agentVendorSign",label:"厂商标识",width:"100"}),e(i,{prop:"agentName",label:"智能体名称",width:"100"}),e(i,{prop:"agentType",label:"模型类型",width:"100"}),e(i,{prop:"usePrice",label:"算力价格",width:"100"}),e(i,{prop:"agentDesc","show-overflow-tooltip":"",label:"描述",width:"200"}),e(i,{prop:"agentSecretToken","show-overflow-tooltip":"",label:"令牌",width:"200"}),e(i,{prop:"modifyTime",label:"创建时间",width:"200"}),e(i,{prop:"agentStatus",label:"状态",width:"160"},{default:n(t=>[y(de(t.row.agentStatus===1?"可用":"不可用"),1)]),_:1}),e(i,{fixed:"right",label:"操作"},{default:n(t=>[e(A,{size:"small",type:"primary",onClick:Re=>O(t.row)},{default:n(()=>[y("修改")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})),[[te,a(S)]]),e(J,null,{default:n(()=>[e(H,{background:"",layout:"prev,pager, next",total:a(E),"current-page":a(m).page,onCurrentChange:N},null,8,["total","current-page"])]),_:1})]),_:1}),e(ee,{modelValue:a(p).isShow,"onUpdate:modelValue":l[14]||(l[14]=t=>a(p).isShow=t),title:"创建/修改智能体",width:"800px"},{default:n(()=>[e(D,{ref_key:"addForm",ref:U,model:a(o),class:"demo-form-inline","label-width":"120px",rules:a(M)},{default:n(()=>[e(r,{label:"厂商标识",prop:"agentVendorSign"},{default:n(()=>[e(v,{modelValue:a(o).agentVendorSign,"onUpdate:modelValue":l[1]||(l[1]=t=>a(o).agentVendorSign=t),placeholder:"请选择"},{default:n(()=>[(u(!0),f(C,null,x(a(V),t=>(u(),w(d,{label:t.name,value:t.sign,key:t.sign},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,{label:"封面图",prop:"agentCover"},{default:n(()=>[e(Q,{ref_key:"uploadImgRef",ref:R,class:"avatar-uploader","before-upload":j,"show-file-list":!1,"http-request":z},{default:n(()=>[k("div",qe,[a(o).agentCover?(u(),f("img",{key:0,src:a(o).agentCover,class:"preview-img"},null,8,Fe)):(u(),f("div",Pe,[e(I,{size:"30",color:"#cdd0d6"},{default:n(()=>[e(X)]),_:1})])),q(k("div",Ne,[e(I,{size:"22",color:"#ffffff",onClick:ue(B,["stop"])},{default:n(()=>[e(K)]),_:1},8,["onClick"])],512),[[ie,a(o).agentCover]])])]),_:1},512)]),_:1}),e(r,{label:"智能体名称",prop:"agentName"},{default:n(()=>[e(g,{class:"input",modelValue:a(o).agentName,"onUpdate:modelValue":l[2]||(l[2]=t=>a(o).agentName=t),placeholder:"智能体名称",maxlength:"20"},null,8,["modelValue"])]),_:1}),e(r,{label:"智能体标识",prop:"agentSign"},{default:n(()=>[e(g,{class:"input",modelValue:a(o).agentSign,"onUpdate:modelValue":l[3]||(l[3]=t=>a(o).agentSign=t),placeholder:"智能体标识"},null,8,["modelValue"])]),_:1}),e(r,{label:"智能体令牌",prop:"agentSecretToken"},{default:n(()=>[e(g,{class:"input",modelValue:a(o).agentSecretToken,"onUpdate:modelValue":l[4]||(l[4]=t=>a(o).agentSecretToken=t),placeholder:"智能体令牌"},null,8,["modelValue"])]),_:1}),e(r,{label:"模型类型",prop:"agentType"},{default:n(()=>[e(v,{modelValue:a(o).agentType,"onUpdate:modelValue":l[5]||(l[5]=t=>a(o).agentType=t),placeholder:"请选择"},{default:n(()=>[(u(!0),f(C,null,x(a(F),t=>(u(),w(d,{label:t.name,value:t.value,key:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(r,{label:"智能体描述",prop:"agentDesc"},{default:n(()=>[e(g,{class:"input",modelValue:a(o).agentDesc,"onUpdate:modelValue":l[6]||(l[6]=t=>a(o).agentDesc=t),placeholder:"智能体描述"},null,8,["modelValue"])]),_:1}),e(r,{label:"模型状态",prop:"agentStatus"},{default:n(()=>[e(L,{modelValue:a(o).agentStatus,"onUpdate:modelValue":l[7]||(l[7]=t=>a(o).agentStatus=t),"active-value":1,"inactive-value":0,"active-text":"启用","inactive-text":"不启用","active-color":"#13ce66","inactive-color":"#ff4949"},null,8,["modelValue"])]),_:1}),e(r,{label:"是否免费",prop:"isMemberFree"},{default:n(()=>[e(L,{modelValue:a(o).isMemberFree,"onUpdate:modelValue":l[8]||(l[8]=t=>a(o).isMemberFree=t),"active-value":1,"inactive-value":0,"active-text":"支持","inactive-text":"不支持","active-color":"#13ce66","inactive-color":"#ff4949"},null,8,["modelValue"])]),_:1}),e(r,{label:"AI算力点数价格",prop:"usePrice"},{default:n(()=>[e(g,{class:"input",modelValue:a(o).usePrice,"onUpdate:modelValue":l[9]||(l[9]=t=>a(o).usePrice=t),placeholder:"AI算力点数价格"},null,8,["modelValue"])]),_:1}),e(r,{label:"展示顺序",prop:"showOrder"},{default:n(()=>[e(g,{class:"input",modelValue:a(o).showOrder,"onUpdate:modelValue":l[10]||(l[10]=t=>a(o).showOrder=t),placeholder:"展示顺序"},null,8,["modelValue"])]),_:1}),e(r,{label:"部署地址",prop:"deployAddress"},{default:n(()=>[e(g,{class:"input",modelValue:a(o).deployAddress,"onUpdate:modelValue":l[11]||(l[11]=t=>a(o).deployAddress=t),placeholder:"部署地址"},null,8,["modelValue"])]),_:1}),e(r,{label:"部署秘钥",prop:"deployAddressSecret"},{default:n(()=>[e(g,{class:"input",modelValue:a(o).deployAddressSecret,"onUpdate:modelValue":l[12]||(l[12]=t=>a(o).deployAddressSecret=t),placeholder:"部署秘钥"},null,8,["modelValue"])]),_:1}),e(r,null,{default:n(()=>[e(A,{type:"primary",onClick:l[13]||(l[13]=t=>Y(a(U)))},{default:n(()=>[y("提交")]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}});const tt=Ue(Oe,[["__scopeId","data-v-5d7c3e02"]]);export{tt as default};
