import{o as e,c as t,f as o}from"./index-6e8b0ade.js";const c={viewBox:"0 0 1024 1024",width:"1.2em",height:"1.2em"},s=o("path",{fill:"currentColor",d:"M160 256H96a32 32 0 0 1 0-64h256V95.936a32 32 0 0 1 32-32h256a32 32 0 0 1 32 32V192h256a32 32 0 1 1 0 64h-64v672a32 32 0 0 1-32 32H192a32 32 0 0 1-32-32V256zm448-64v-64H416v64h192zM224 896h576V256H224v640zm192-128a32 32 0 0 1-32-32V416a32 32 0 0 1 64 0v320a32 32 0 0 1-32 32zm192 0a32 32 0 0 1-32-32V416a32 32 0 0 1 64 0v320a32 32 0 0 1-32 32z"},null,-1),_=[s];function h(n,a){return e(),t("svg",c,_)}const u={name:"ep-delete",render:h},r={viewBox:"0 0 1024 1024",width:"1.2em",height:"1.2em"},i=o("path",{fill:"currentColor",d:"M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 1 1 0 64H544v352a32 32 0 1 1-64 0V544H128a32 32 0 0 1 0-64h352z"},null,-1),l=[i];function d(n,a){return e(),t("svg",r,l)}const p={name:"ep-plus",render:d};export{p as _,u as a};
