<script setup lang="ts">
import {
  editScene<PERSON>pi,
  getSceneListApi,
  uploadImg,
  createScene<PERSON><PERSON>,
  getSceneLastListApi,
  delSceneApi,
  getAbleUseModelsApi,
  getCategoryCodeApi,
  getKnowLedgeListApi,
} from '@/api/index';
import type { FormInstance, UploadInstance, UploadProps } from 'element-plus';

//获取知识库列表
const knowLedgeReq = reactive({
  merchantGuid: '',
  pageSize: 100,
  page: 1,
});
const knowLedgeList: any = ref([]);
const getKnowLedgeList = async () => {
  let res = await getKnowLedgeListApi(knowLedgeReq);
  knowLedgeList.value = res.data.data;
};
let modelList: any = ref([]);
//获取模型列表
const getAbleUseModels = async () => {
  let res = await getAbleUseModelsApi();
  modelList.value = res.data;
};
getAbleUseModels();

const getSceneListReq = reactive({
  merchantGuid: '',
  cate_type: '',
});
const sceneList = ref([]);
let loading = ref(false);
const getSceneList = async () => {
  loading.value = true;
  let res = await getSceneListApi(getSceneListReq);
  sceneList.value = res.data;
  loading.value = false;
};

const onAddScene = () => {
  dialogVisible.value = true;
  dialogtype.value = 'add';
  getSceneLastList();
  getKnowLedgeList();
};

let modeStatus = ref('');
const modeList = reactive([
  {
    id: 0,
    type: '',
    name: '全部',
  },
  {
    id: 1,
    type: 'text',
    name: '文本',
  },
  {
    id: 2,
    type: 'image',
    name: '图像',
  },
]);
const onChangeMode = (value) => {
  modeStatus.value = value;
  getSceneListReq.cate_type = value;
};
//搜索
const onSearch = () => {
  getSceneList();
};

let dialogVisible = ref(false);
let createReqFrom = reactive({
  pid: 0,
  title: '',
  image: '',
  desc: '',
  sort: 0,
  status: 200,
  chatgtp_content: '',
  cate_type: 'text',
  merchantGuid: '',
  ai_model: '',
  able_lunci: 1,
  build_knowledge_guids: [],
  knowledge_use_params:
    "针对用户的问题，我为你提供了知识库内容，知识库内容中的score代表了匹配度，满分是0.99，分数大于0.3就必须高度重视'，越高越匹配，content代表了知识库的内容。请你参考知识库的内容进行回答，尽量使用我为你提供的知识库内容，知识库内容是：",
});

const uploadImgRef = ref<UploadInstance>();
const beforeAvatarUpload: UploadProps['beforeUpload'] = (rawFile) => {
  let fileTypes = ['image/jpg', 'image/png', 'image/jpeg'];
  if (!fileTypes.includes(rawFile.type)) {
    ElMessage.warning('当前图片仅支持格式为：' + fileTypes.join(' ，'));
    return false;
  } else if (rawFile.size / 1024 / 1024 > 2) {
    ElMessage.error('图片必须小于2M');
    return false;
  }
  return true;
};
const upload = async (file) => {
  const formData = new FormData();
  // 添加要上传的文件名和文件对象,key就是你上传文件的字段名
  formData.append('img', file.file);
  let res = await uploadImg(formData);
  createReqFrom.image = res.data;
};
const handleRemove = () => {
  createReqFrom.image = '';
};
const addForm = ref<FormInstance>();
// const rules = reactive<FormRules>({
//   merchantGuid: [{ required: true, message: '请输入角色名称', trigger: 'change' }],
// });
const onSubmit = async (formEl) => {
  formEl.validate(async (valid) => {
    if (!valid) {
      return;
    }
    try {
      if (dialogtype.value === 'add') {
        await createSceneApi(createReqFrom);
        ElMessage.success('新增成功');
      } else if (dialogtype.value === 'edit') {
        await editSceneApi(createReqFrom);
        ElMessage.success('修改成功');
      }
      nextTick(() => {
        formEl.resetFields();
        dialogVisible.value = false;
      });
      getSceneList();
    } catch (error: any) {
      ElMessage.error(error);
      throw new Error(error);
    }
  });
};
let dialogtype = ref('');
const onEdit = (item) => {
  createReqFrom = Object.assign(createReqFrom, item);
  createReqFrom.merchantGuid = item.merchant_guid;
  dialogVisible.value = true;
  dialogtype.value = 'edit';
  editSceneLastList();
  getKnowLedgeList();
};
const onDelete = async (item) => {
  let req = {
    id: item.id,
  };
  await delSceneApi(req);
  ElMessage.success('删除成功');
  getSceneList();
};
const onPopRadio = () => {
  if (createReqFrom.cate_type === 'image') {
    createReqFrom.build_knowledge_guids = [];
  }
  getSceneLastList();
};
const sceneLastList: any = ref([]);
const getSceneLastList = async () => {
  let res = await getSceneLastListApi(createReqFrom);
  sceneLastList.value = res.data;
  sceneLastList.value.push({ title: '无(一级)', id: 0 });
  createReqFrom.pid = sceneLastList.value[0].id;
};
const editSceneLastList = async () => {
  let res = await getSceneLastListApi(createReqFrom);
  sceneLastList.value = res.data;
};
// getTenantList();
getSceneList();
let qrcodePopConfig = reactive({
  isShow: false,
  url: '',
  title: '',
});
const onCreateQRcode = async (item) => {
  qrcodePopConfig.url = '';
  qrcodePopConfig.title = item.title;
  qrcodePopConfig.isShow = true;
  let res = await getCategoryCodeApi({ id: item.id });
  qrcodePopConfig.url = res.data;
};
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main>
        <div class="header-box">
          <el-form :inline="true" :model="getSceneListReq" class="demo-form-inline">
            <el-form-item><el-button type="primary" @click="onAddScene">新增场景</el-button></el-form-item>
            <!-- <el-form-item label="所属商家">
              <el-select v-model="getSceneListReq.merchantGuid" placeholder="请选择">
                <el-option label="全部" value="0" />
                <el-option :label="item.merchantName" :value="item.guid" v-for="item in tenantList" :key="item.guid" />
              </el-select>
            </el-form-item> -->
            <el-form-item label="场景类型">
              <div class="mode-box">
                <div :class="['item', { active: modeStatus === item.type }]" v-for="(item, index) in modeList"
                  :key="index" @click="onChangeMode(item.type)">
                  {{ item.name }}
                </div>
              </div>
            </el-form-item>
            <el-form-item> <el-button type="primary" @click="onSearch">搜索</el-button></el-form-item>
          </el-form>
        </div>
        <el-table v-loading="loading" :data="sceneList" border style="width: 100%" row-key="id"
          :tree-props="{ children: 'child' }">
          <el-table-column prop="id" label="场景id" width="120" />
          <el-table-column prop="title" label="名称" width="150" />
          <el-table-column prop="desc" label="描述" width="150" />
          <el-table-column prop="image" :show-overflow-tooltip="true" label="图片" width="100">
            <template #default="scope">
              <el-image :src="scope.row.image" style="width: 50px; height: 50px; border-radius: 50%"></el-image>
            </template>
          </el-table-column>
          <el-table-column prop="sort" label="排序" width="50" />
          <el-table-column prop="status" label="状态" width="150">
            <template #default="scope">
              <el-switch v-model="scope.row.status" disabled active-text="正常" inactive-text="隐藏" :active-value="200"
                :inactive-value="100" />
            </template>
          </el-table-column>
          <el-table-column prop="chatgtp_content" label="提示语" width="250" :show-overflow-tooltip="true" />
          <el-table-column prop="cate_type" label="类型" width="100">
            <template #default="scope">
              {{ scope.row.cate_type === 'image' ? '图片' : '文本' }}
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button v-if="scope.row.pid > 0" size="small" type="primary"
                @click="onCreateQRcode(scope.row)">生成入口</el-button>
              <el-button size="small" type="primary" @click="onEdit(scope.row)">编辑</el-button>
              <el-button size="small" type="warning" @click="onDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-main>
    </el-container>
    <el-dialog v-model="dialogVisible" title="新增/编辑 场景" width="1000px">
      <el-form ref="addForm" :model="createReqFrom" class="demo-form-inline" label-width="100px">
        <el-form-item label="类型" prop="cate_type" @change="onPopRadio">
          <el-radio-group v-model="createReqFrom.cate_type">
            <el-radio-button label="text">文本</el-radio-button>
            <el-radio-button label="image">图片</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="上级分类" prop="pid">
          <el-select v-model="createReqFrom.pid" placeholder="请选择">
            <el-option :label="item.title" :value="item.id" v-for="item in sceneLastList" :key="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="名称" prop="title">
          <el-input v-model="createReqFrom.title" placeholder="名称" />
        </el-form-item>
        <el-form-item label="头像" prop="iamge">
          <el-upload ref="uploadImgRef" class="avatar-uploader" :before-upload="beforeAvatarUpload"
            :show-file-list="false" :http-request="upload">
            <div class="upload-img-box">
              <img v-if="createReqFrom.image" :src="createReqFrom.image" class="preview-img" />
              <div class="upload-btn" v-else>
                <el-icon size="30" color="#cdd0d6"><i-ep-Plus /></el-icon>
              </div>
              <div class="operate-box" v-show="createReqFrom.image">
                <el-icon size="22" color="#ffffff" @click.stop="handleRemove"><i-ep-Delete /></el-icon>
              </div>
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="描述" prop="desc">
          <el-input v-model="createReqFrom.desc" placeholder="描述" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="createReqFrom.sort" :min="0" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-switch v-model="createReqFrom.status" active-text="正常" inactive-text="隐藏" :active-value="200"
            :inactive-value="100" />
        </el-form-item>
        <el-form-item label="提示语" prop="chatgtp_content">
          <v-md-editor v-model="createReqFrom.chatgtp_content" height="400px"></v-md-editor>
        </el-form-item>
        <el-form-item label="选择知识库" prop="build_knowledge_guids" v-if="createReqFrom.cate_type === 'text'">
          <el-select v-model="createReqFrom.build_knowledge_guids" placeholder="请选择" multiple :multiple-limit="1">
            <el-option :label="item.knowledgeTitle" :value="item.guid" v-for="item in knowLedgeList" :key="item.guid" />
          </el-select>
        </el-form-item>
        <el-form-item label="知识库参数" prop="knowledge_use_params" v-if="createReqFrom.cate_type === 'text'">
          <el-input v-model="createReqFrom.knowledge_use_params" />
        </el-form-item>
        <el-form-item label="选择Ai模型" prop="ai_model">
          <el-radio-group v-model="createReqFrom.ai_model" class="input">
            <el-radio :label="item.id" size="large" v-for="item in modelList" :key="item.id">{{ item.name }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="able_lunci" label="是否开启上下文">
          <el-switch v-model="createReqFrom.able_lunci" active-text="开启" inactive-text="关闭" :active-value="1"
            :inactive-value="0" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit(addForm)">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-dialog v-model="qrcodePopConfig.isShow" :title="qrcodePopConfig.title" width="300px" align-center>
      <div class="qrcode-pop-box">
        <el-image style="width: 200px; height: 200px" :src="qrcodePopConfig.url">
          <template #error>
            <div class="image-slot">
              <el-icon><i-ep-Loading /></el-icon>
            </div>
          </template>
        </el-image>
      </div>
    </el-dialog>
  </div>
  <!-- const createReqFrom = reactive({
  pid: '0',
  title: '',
  image: '',
  desc: '',
  sort: '',
  status: '100',
  chatgtp_content: '',
  cate_type: 'image',
  merchantGuid: '',
}); -->
</template>

<style scoped lang="scss">
.qrcode-pop-box {
  display: flex;
  justify-content: center;
  align-items: center;

  .image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: var(--el-fill-color-light);
    color: var(--el-text-color-secondary);
    font-size: 30px;
  }

  .image-slot .el-icon {
    font-size: 30px;
  }
}

.mode-box {
  background-color: #eee;
  display: flex;
  padding: 4px 0px;
  border-radius: 6px;
  width: 300px;

  .item {
    background-color: #fff;
    // padding: 5px;
    margin: 0 5px;
    flex: 1;
    border-radius: 6px;
    text-align: center;
    cursor: pointer;
    height: 26px;
    line-height: 26px;

    &.active {
      background-color: #409eff;
      color: #fff;
    }
  }
}

.avatar-uploader {
  margin-right: 20px;
}

.upload-img-box {
  width: 120px;
  height: 120px;
  border: 1px solid #cdd0d6;
  box-sizing: border-box;
  position: relative;
  border-radius: 6px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;

  .preview-img {
    width: 100%;
    height: 100%;
  }

  .operate-box {
    position: absolute;
    z-index: 9;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.5);
    visibility: hidden;
  }

  &:hover .operate-box {
    visibility: visible;
  }
}
</style>
