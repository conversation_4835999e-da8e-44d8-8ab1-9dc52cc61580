<script setup lang="ts">
import {
  getTenantListApi,
  getAiAgentVendorListApi,
  merchantAiAgentCreateApi,
  merchantAiAgentUpdateApi,
  merchantAgentListApi,
  uploadImg,
} from '@/api';
import type { FormInstance, FormRules, UploadProps, UploadInstance } from 'element-plus';
import { useRouter } from 'vue-router';
const router = useRouter();
//分页
const total = ref(0);
const loading = ref(false);
const reqForm = reactive({
  merchantGuid: '',
  agentVendorSign: 'coze',
  agentType: 'text',
  pageSize: 10,
  page: 1,
});
// { name: '全部', sign: '' }
let modeList = reactive<any>([]);
let typeList = reactive([
  { name: '文本', value: 'text' },
  // { name: '图片', value: 'image' },
  // { name: '音频', value: 'audio' },
  // { name: '视频', value: 'video' },
]);
let goodsList = ref([]);
let tenantList: any = ref([]);

const onChangeMode = (value) => {
  reqForm.agentVendorSign = value;
  getList();
};
//获取产品列表
const getList = async () => {
  loading.value = true;
  let goodsRes = await merchantAgentListApi(reqForm);
  loading.value = false;
  total.value = goodsRes.data.total;
  goodsList.value = goodsRes.data.data;
};
//获取商户列表
// const getTenantList = async () => {
//   let res = await getTenantListApi();
//   tenantList.value = res.data;
//   reqForm.merchantGuid = tenantList.value[0].guid;

// };

const getAiAgentVendorList = async () => {
  let res = await getAiAgentVendorListApi();
  // modeList = Object.assign(modeList, res.data);
  modeList.push(...res.data);
  reqForm.agentVendorSign = modeList[0].sign;
  getList();
};

const handlePageChang = async (page) => {
  reqForm.page = page;
  getList();
};
const onEdit = (item) => {
  addReq = Object.assign(addReq, item);
  dialogConfig.isShow = true;
  dialogConfig.dialogtype = 'EDIT';
};
const rules = reactive<FormRules>({
  // merchantGuid: [{ required: true, message: '请选择商家', trigger: 'change' }],
  agentVendorSign: [{ required: true, message: '请输入厂商标识', trigger: 'change' }],
  agentName: [{ required: true, message: '请输入智能体名称', trigger: 'change' }],
  agentSecretToken: [{ required: true, message: '请输入智能体令牌', trigger: 'change' }],
  agentCover: [{ required: true, message: '请上传封面图', trigger: 'change' }],
  agentType: [{ required: true, message: '请选择模型类型', trigger: 'change' }],
  agentDesc: [{ required: true, message: '请输入厂商标识', trigger: 'change' }],
  agentStatus: [{ required: true, message: '请选择模型状态', trigger: 'change' }],
  isMemberFree: [{ required: true, message: '请选择是否支持免费', trigger: 'change' }],
  usePrice: [{ required: true, message: '请输入AI算力点数价格', trigger: 'change' }],
  showOrder: [{ required: true, message: '请输入展示顺序', trigger: 'change' }],
});
interface AddReq {
  guid: string;
  merchantGuid: string;
  agentVendorSign: string;
  agentName: string;
  agentSign: string;
  agentSecretToken: string;
  agentCover: string;
  agentType: string;
  agentDesc: string;
  agentStatus: number;
  isMemberFree: number;
  usePrice: string;
  showOrder: string;
  deployAddress: string;
  deployAddressSecret: string;
}
const addForm = ref<FormInstance>();
let addReq: AddReq = reactive({
  guid: '',
  merchantGuid: '',
  agentVendorSign: '',
  agentName: '',
  agentSign: '',
  agentSecretToken: '',
  agentType: '',
  agentDesc: '',
  agentStatus: 1,
  isMemberFree: 1,
  agentCover: '',
  usePrice: '1',
  showOrder: '1',
  deployAddress: '',
  deployAddressSecret: '',
});
const dialogConfig = reactive({
  dialogtype: 'ADD',
  isShow: false,
});

const uploadImgRef = ref<UploadInstance>();
const beforeLogoUpload: UploadProps['beforeUpload'] = (rawFile) => {
  let fileTypes = ['image/jpg', 'image/png', 'image/jpeg'];
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.src = URL.createObjectURL(rawFile);
    if (!fileTypes.includes(rawFile.type)) {
      ElMessage.warning('当前图片仅支持格式为：' + fileTypes.join(' ，'));
      reject(false);
      return;
    }
    resolve(true);
  });
};
const logoUpload = async (file) => {
  const formData = new FormData();
  // 添加要上传的文件名和文件对象,key就是你上传文件的字段名
  formData.append('img', file.file);
  let res = await uploadImg(formData);
  addReq.agentCover = res.data;
};
const handleLogoRemove = () => {
  addReq.agentCover = '';
};
const onAdd = () => {
  dialogConfig.isShow = true;
  dialogConfig.dialogtype = 'ADD';
};

const onSubmit = async (formEl) => {
  formEl.validate(async (valid) => {
    if (!valid) {
      return;
    }
    try {
      if (dialogConfig.dialogtype === 'ADD') {
        await merchantAiAgentCreateApi(addReq);
        ElMessage.success('新增成功');
      } else if (dialogConfig.dialogtype === 'EDIT') {
        await merchantAiAgentUpdateApi(addReq);
        ElMessage.success('修改成功');
      }
      nextTick(() => {
        formEl.resetFields();
        dialogConfig.isShow = false;
      });
      getList();
    } catch (error: any) {
      ElMessage.error(error);
      throw new Error(error);
    }
  });
};
getAiAgentVendorList();
// getTenantList();
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main v-loading="loading">
        <div class="hearder-box">
          <el-form :inline="true" :model="reqForm" class="demo-form-inline">
            <!-- <el-form-item label="所属商家">
              <el-select v-model="reqForm.merchantGuid" placeholder="请选择" @change="getList">
                <el-option :label="item.merchantName" :value="item.guid" v-for="item in tenantList" :key="item.guid" />
              </el-select>
            </el-form-item> -->
            <el-form-item label="所属厂商">
              <el-select v-model="reqForm.agentVendorSign" placeholder="请选择" @change="getList">
                <!-- <el-option label="无" value="" /> -->
                <el-option :label="item.name" :value="item.sign" v-for="item in modeList" :key="item.sign" />
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="厂商">
              <div class="mode-box">
                <div
                  :class="['item', { active: reqForm.agentVendorSign === item.sign }]"
                  v-for="(item, index) in modeList"
                  :key="index"
                  @click="onChangeMode(item.sign)">
                  {{ item.name }}
                </div>
              </div>
            </el-form-item> -->
            <el-form-item>
              <el-button type="primary" @click="onAdd">新增</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table :data="goodsList" border style="width: 100%">
          <el-table-column prop="sysId" label="sysId" width="80" />
          <el-table-column prop="agentVendorSign" label="厂商标识" width="100" />
          <el-table-column prop="agentName" label="智能体名称" width="100" />
          <el-table-column prop="agentType" label="模型类型" width="100" />
          <el-table-column prop="usePrice" label="算力价格" width="100" />
          <el-table-column prop="agentDesc" show-overflow-tooltip label="描述" width="200" />
          <el-table-column prop="agentSecretToken" show-overflow-tooltip label="令牌" width="200" />
          <el-table-column prop="modifyTime" label="创建时间" width="200"> </el-table-column>
          <el-table-column prop="agentStatus" label="状态" width="160">
            <template #default="scope">
              {{ scope.row.agentStatus === 1 ? '可用' : '不可用' }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作">
            <template #default="scope">
              <el-button size="small" type="primary" @click="onEdit(scope.row)">修改</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-main>
      <el-footer>
        <el-pagination background layout="prev,pager, next" :total="total" :current-page="reqForm.page"
          @current-change="handlePageChang" />
      </el-footer>
    </el-container>
    <el-dialog v-model="dialogConfig.isShow" title="创建/修改智能体" width="800px">
      <el-form ref="addForm" :model="addReq" class="demo-form-inline" label-width="120px" :rules="rules">
        <!-- <el-form-item label="所属商家" prop="merchantGuid">
          <el-select v-model="addReq.merchantGuid" placeholder="请选择">
            <el-option :label="item.merchantName" :value="item.guid" v-for="item in tenantList" :key="item.guid" />
          </el-select>
        </el-form-item> -->
        <el-form-item label="厂商标识" prop="agentVendorSign">
          <!-- <el-input class="input" v-model="addReq.agentVendorSign" placeholder="厂商标识" maxlength="20" /> -->
          <el-select v-model="addReq.agentVendorSign" placeholder="请选择">
            <el-option :label="item.name" :value="item.sign" v-for="item in modeList" :key="item.sign" />
          </el-select>
        </el-form-item>
        <el-form-item label="封面图" prop="agentCover">
          <el-upload ref="uploadImgRef" class="avatar-uploader" :before-upload="beforeLogoUpload"
            :show-file-list="false" :http-request="logoUpload">
            <div class="upload-img-box">
              <img v-if="addReq.agentCover" :src="addReq.agentCover" class="preview-img" />
              <div class="upload-btn" v-else>
                <el-icon size="30" color="#cdd0d6"><i-ep-Plus /></el-icon>
              </div>
              <div class="operate-box" v-show="addReq.agentCover">
                <el-icon size="22" color="#ffffff" @click.stop="handleLogoRemove"><i-ep-Delete /></el-icon>
              </div>
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="智能体名称" prop="agentName">
          <el-input class="input" v-model="addReq.agentName" placeholder="智能体名称" maxlength="20" />
        </el-form-item>
        <el-form-item label="智能体标识" prop="agentSign">
          <el-input class="input" v-model="addReq.agentSign" placeholder="智能体标识" />
        </el-form-item>
        <el-form-item label="智能体令牌" prop="agentSecretToken">
          <el-input class="input" v-model="addReq.agentSecretToken" placeholder="智能体令牌" />
        </el-form-item>
        <el-form-item label="模型类型" prop="agentType">
          <el-select v-model="addReq.agentType" placeholder="请选择">
            <el-option :label="item.name" :value="item.value" v-for="item in typeList" :key="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="智能体描述" prop="agentDesc">
          <el-input class="input" v-model="addReq.agentDesc" placeholder="智能体描述" />
        </el-form-item>
        <el-form-item label="模型状态" prop="agentStatus">
          <el-switch v-model="addReq.agentStatus" :active-value="1" :inactive-value="0" active-text="启用"
            inactive-text="不启用" active-color="#13ce66" inactive-color="#ff4949">
          </el-switch>
        </el-form-item>
        <el-form-item label="是否免费" prop="isMemberFree">
          <el-switch v-model="addReq.isMemberFree" :active-value="1" :inactive-value="0" active-text="支持"
            inactive-text="不支持" active-color="#13ce66" inactive-color="#ff4949">
          </el-switch>
        </el-form-item>
        <el-form-item label="AI算力点数价格" prop="usePrice">
          <el-input class="input" v-model="addReq.usePrice" placeholder="AI算力点数价格" />
        </el-form-item>
        <el-form-item label="展示顺序" prop="showOrder">
          <el-input class="input" v-model="addReq.showOrder" placeholder="展示顺序" />
        </el-form-item>
        <el-form-item label="部署地址" prop="deployAddress">
          <el-input class="input" v-model="addReq.deployAddress" placeholder="部署地址" />
        </el-form-item>
        <el-form-item label="部署秘钥" prop="deployAddressSecret">
          <el-input class="input" v-model="addReq.deployAddressSecret" placeholder="部署秘钥" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit(addForm)">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.pop-code-flex {
  display: flex;

  .btn {
    margin-left: 10px;
  }
}

.mode-box {
  background-color: #eee;
  display: flex;
  padding: 4px 0px;
  border-radius: 6px;
  width: 200px;

  .item {
    background-color: #fff;
    //padding: 0 5px;
    margin: 0 5px;
    flex: 1;
    border-radius: 6px;
    text-align: center;
    cursor: pointer;
    height: 26px;
    line-height: 26px;

    &.active {
      background-color: #409eff;
      color: #fff;
    }
  }
}

.upload-img-box {
  width: 120px;
  height: 120px;
  border: 1px solid #cdd0d6;
  box-sizing: border-box;
  position: relative;
  border-radius: 6px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;

  .preview-img {
    width: 100%;
    height: 100%;
  }

  .operate-box {
    position: absolute;
    z-index: 9;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.5);
    visibility: hidden;
  }

  &:hover .operate-box {
    visibility: visible;
  }
}
</style>
