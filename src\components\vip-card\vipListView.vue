<script setup lang="ts">
import '@wangeditor/editor/dist/css/style.css';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import { vipCardList, uploadImg, createCipCard, deleteCipCard, updateCipCard } from '@/api';
import type { FormInstance, UploadProps, FormRules } from 'element-plus';
import { useAdminCommonStore } from '@/stores/adminCommon';
const store = useAdminCommonStore();

const editorRef = shallowRef();
const editorRef2 = shallowRef();

const toolbarConfig = {
  excludeKeys: ['group-image', 'insertVideo', 'group-video'],
};
const handleCreated = (editor) => {
  editorRef.value = editor; // 记录 editor 实例，重要！
};
const handleCreated2 = (editor) => {
  editorRef2.value = editor;
};
//分页
const total = ref(0);
const loading = ref(false);
let merchantGuid = computed(() => {
  return store.getTenantInfo.guid;
});
const reqForm = reactive({
  merchantGuid: '',
  pageSize: 10,
  page: 1,
});
let goodsList = ref([]);
//获取产品列表
const getList = async () => {
  reqForm.merchantGuid = merchantGuid.value;
  loading.value = true;
  let goodsRes = await vipCardList(reqForm);
  loading.value = false;
  // total.value = goodsRes.data.total;
  goodsList.value = goodsRes.data;
};
const handlePageChang = async (page) => {
  reqForm.page = page;
  getList();
};
//搜索
const onSearch = () => {
  getList();
};
const dialogConfig = reactive({
  dialogtype: 'ADD',
  isShow: false,
});
const rules = reactive<FormRules>({
  cardName: [{ required: true, message: '请输入会员卡名称', trigger: 'change' }],
  bgImg: [{ required: true, message: '上传背景', trigger: 'change' }],
});
type ConfigType = {
  type: string;
  typeDesc: string;
  isEnable: boolean;
};
interface AddReq {
  cardUseConfig: ConfigType[];
  merchantGuid: string;
  cardName: string;
  cardType: string;
  cardDesc: string;
  cardPrice: number;
  cardNotice: string;
  bgImg: string;
  cardUseConfigIndex: string[];
}
const cardConfigType = [
  {
    type: 'chat',
    typeDesc: 'AI聊天',
    isEnable: true,
  },
  {
    type: 'img',
    typeDesc: 'AI绘画',
    isEnable: true,
  },
  {
    type: 'shuziren',
    typeDesc: 'AI数字人',
    isEnable: true,
  },
];
const cardType = [
  {
    name: '月卡',
    value: '1',
  },
  {
    name: '季卡',
    value: '2',
  },
  {
    name: '年卡',
    value: '3',
  },
];
const addForm = ref<FormInstance>();
let addReq: AddReq = reactive({
  merchantGuid: '',
  cardName: '',
  cardType: '1',
  cardDesc: '',
  cardPrice: 0,
  cardNotice: '',
  cardUseConfig: [],
  cardUseConfigIndex: [],
  bgImg: '',
});
const onAdd = () => {
  dialogConfig.isShow = true;
  dialogConfig.dialogtype = 'ADD';
};
const beforeAvatarUpload: UploadProps['beforeUpload'] = (rawFile) => {
  let fileTypes = ['image/jpg', 'image/png', 'image/jpeg'];
  if (!fileTypes.includes(rawFile.type)) {
    ElMessage.warning('当前图片仅支持格式为：' + fileTypes.join(' ，'));
    return false;
  }
};
const upload = async (file) => {
  const formData = new FormData();
  // 添加要上传的文件名和文件对象,key就是你上传文件的字段名
  formData.append('img', file.file);
  let res = await uploadImg(formData);
  addReq.bgImg = res.data;
};
const handleRemove = () => {
  addReq.bgImg = '';
};
const onEdit = (item) => {
  addReq = Object.assign(addReq, item);
  addReq.cardUseConfigIndex = [];
  item.cardUseConfig.forEach((element) => {
    addReq.cardUseConfigIndex.push(element.typeDesc);
  });
  addReq.cardPrice = parseFloat(item.cardPrice);
  dialogConfig.isShow = true;
  dialogConfig.dialogtype = 'EDIT';
};
const handleDelete = async (item) => {
  try {
    let guid = item.guid;
    loading.value = true;
    await deleteCipCard({ guid });
    loading.value = false;
    getList();
    ElMessage.success('删除成功');
  } catch (error: any) {
    loading.value = false;
    ElMessage.success(error);
  }
};
const onSubmit = async (formEl) => {
  formEl.validate(async (valid) => {
    if (!valid) {
      return;
    }
    try {
      addReq.merchantGuid = merchantGuid.value;
      if (dialogConfig.dialogtype === 'ADD') {
        addReq.cardUseConfigIndex.forEach((item) => {
          // cardConfigType[item]);
          let obj = cardConfigType.find((f) => {
            return f.typeDesc == item;
          });
          addReq.cardUseConfig.push(obj as ConfigType);
        });
        await createCipCard(addReq);
        ElMessage.success('新增成功');
      } else if (dialogConfig.dialogtype === 'EDIT') {
        await updateCipCard(addReq);
        ElMessage.success('修改成功');
      }
      nextTick(() => {
        formEl.resetFields();
        dialogConfig.isShow = false;
      });
      getList();
    } catch (error: any) {
      ElMessage.error(error);
      throw new Error(error);
    }
  });
};
// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value;
  const editor2 = editorRef2.value;
  if (editor2 == null) return;
  editor2.destroy();
  if (editor == null) return;
  editor.destroy();
});
getList();
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main v-loading="loading">
        <div class="hearder-box">
          <el-form :inline="true" :model="reqForm" class="demo-form-inline">
            <el-form-item>
              <el-button type="primary" @click="onAdd">新增</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table :data="goodsList" border style="width: 100%">
          <el-table-column prop="sysId" label="CardId" width="80" />
          <el-table-column prop="cardName" label="会员卡名称" width="180" />
          <el-table-column prop="cardPrice" label="会员卡价格" width="120" />
          <el-table-column prop="bgImg" label="会员卡背景" width="120">
            <template #default="scope">
              <el-image :src="scope.row.bgImg" style="width: 50px; height: 50px"></el-image>
            </template>
          </el-table-column>
          <el-table-column prop="cardType" label="会员卡类型" width="120" />
          <el-table-column prop="cardUseConfig" label="会员卡配置">
            <template #default="scope">
              <span v-for="item in scope.row.cardUseConfig">{{ item.typeDesc }} &nbsp;</span>
            </template>
          </el-table-column>
          <el-table-column prop="cardNotice" label="会员卡说明">
            <template #default="scope">
              <div v-html="scope.row.cardNotice"></div>
            </template>
          </el-table-column>
          <el-table-column prop="cardDesc" label="会员卡描述" />
          <el-table-column label="操作">
            <template #default="scope">
              <el-button size="small" type="primary" @click="onEdit(scope.row)">编辑</el-button>
              <el-popconfirm confirm-button-text="确认" cancel-button-text="取消" icon-color="red" title="是否删除该会员卡?"
                @confirm="handleDelete(scope.row)">
                <!-- @cancel="cancelEvent" -->
                <template #reference>
                  <el-button size="small" type="danger">删除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </el-main>
      <el-footer>
        <el-pagination background layout="prev,pager, next" :total="total" :current-page="reqForm.page"
          @current-change="handlePageChang" />
      </el-footer>
    </el-container>
    <el-dialog v-model="dialogConfig.isShow" title="创建修改会员卡" width="1000px">
      <el-form ref="addForm" :model="addReq" class="demo-form-inline" label-width="120px" :rules="rules">
        <el-form-item label="会员卡可用权益">
          <el-checkbox-group v-model="addReq.cardUseConfigIndex">
            <el-checkbox v-for="(item, index) in cardConfigType" :value="index" :label="item.typeDesc" />
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="会员卡名称" prop="cardName">
          <el-input v-model="addReq.cardName" placeholder="名称" />
        </el-form-item>
        <el-form-item label="会员卡类型" prop="cardType">
          <el-select v-model="addReq.cardType" class="m-2" placeholder="Select" size="large" style="width: 240px">
            <el-option v-for="item in cardType" :key="item.value" :label="item.name" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="会员卡描述" prop="cardDesc">
          <!-- <el-input v-model="addReq.cardDesc" placeholder="描述" /> -->
          <!-- <v-md-editor v-model="addReq.cardDesc" height="200px"></v-md-editor> -->
          <div style="border: 1px solid #ccc">
            <Toolbar style="border-bottom: 1px solid #ccc" :defaultConfig="toolbarConfig" :editor="editorRef"
              mode="default" />
            <Editor style="height: 300px; overflow-y: hidden" v-model="addReq.cardDesc" mode="default"
              @onCreated="handleCreated" />
          </div>
        </el-form-item>
        <el-form-item label="会员卡说明" prop="cardNotice">
          <!-- <el-input v-model="addReq.cardNotice" placeholder="说明" /> -->
          <!-- <v-md-editor v-model="addReq.cardNotice" height="200px"></v-md-editor> -->
          <div style="border: 1px solid #ccc">
            <Toolbar style="border-bottom: 1px solid #ccc" :defaultConfig="toolbarConfig" :editor="editorRef2"
              mode="default" />
            <Editor style="height: 300px; overflow-y: hidden" v-model="addReq.cardNotice" mode="default"
              @onCreated="handleCreated2" />
          </div>
        </el-form-item>
        <el-form-item label="会员卡价格" prop="cardPrice">
          <el-input-number v-model="addReq.cardPrice" :precision="2" :step="0.01" />
        </el-form-item>
        <el-form-item label="会员卡背景" prop="bgImg">
          <el-upload ref="uploadImgRef" class="avatar-uploader" :before-upload="beforeAvatarUpload"
            :show-file-list="false" :http-request="upload">
            <div class="upload-img-box">
              <img v-if="addReq.bgImg" :src="addReq.bgImg" class="preview-img" />
              <div class="upload-btn" v-else>
                <el-icon size="30" color="#cdd0d6"><i-ep-Plus /></el-icon>
              </div>
              <div class="operate-box" v-show="addReq.bgImg" @click.stop="handleRemove">
                <el-icon size="22" color="#ffffff"><i-ep-Delete /></el-icon>
              </div>
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit(addForm)">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.upload-img-box {
  width: 120px;
  height: 120px;
  border: 1px solid #cdd0d6;
  box-sizing: border-box;
  position: relative;
  border-radius: 6px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;

  .preview-img {
    width: 100%;
    height: 100%;
  }

  .operate-box {
    position: absolute;
    z-index: 9;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.5);
    visibility: hidden;
  }

  &:hover .operate-box {
    visibility: visible;
  }
}
</style>
