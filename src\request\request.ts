import { randomStr, queryStr } from '@/utils/random';
import { autographFun } from '@/utils/signature';
import { useAdminCommonStore } from '@/stores/adminCommon';
import axios from 'axios';

import router from '@/router/inspect';

const request = axios.create({
  baseURL: import.meta.env.VITE_API_URL2,
});
const request_xg = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
});
const requestUse = (config: any) => {
  const store = useAdminCommonStore();
  // do something before request is sent
  // config.headers['tourist-no'] = store.state.user.random_32
  // config.headers['version'] = '1.0.0'
  config.urlSuffix = {
    //应用类型
    app_type: 'admin',
    app_guid: 'default',
    //当前时间戳
    expires: parseInt((new Date().getTime() / 1000).toFixed(0)),
    token: store.getToken(),
    noncestr: randomStr(true, true, true, 32),
  };
  config.urlSuffix.signature = encodeURIComponent(autographFun(config)); //签名
  config.url = config.url + queryStr(config.urlSuffix, config.url);
  return config;
};
const responseUse = (res: any) => {
  const store = useAdminCommonStore();
  if (res.data.code === 704001) {
    store.outLogin();
    router.push('/login');
    ElMessage.error('登录已过期，请重新登录');
  } else if (res.data.code !== 0) {
    ElMessage.error(res.data.msg || '系统错误');
  }
  return res.data.code === 0 ? res.data : Promise.reject(res.data.msg);
};
request.interceptors.request.use(requestUse, (error) => {
  return Promise.reject(error);
});

request.interceptors.response.use(responseUse, (error) => {
  const msg: string = error.msg || '系统错误！';
  ElMessage.error(msg);
  return Promise.reject(error);
});
request_xg.interceptors.request.use(requestUse, (error) => {
  return Promise.reject(error);
});

request_xg.interceptors.response.use(responseUse, (error) => {
  const msg: string = error.msg || '系统错误！';
  ElMessage.error(msg);
  return Promise.reject(error);
});
export { request, request_xg };
