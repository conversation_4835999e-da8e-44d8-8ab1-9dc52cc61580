<script setup lang="ts">
import { getCardListApi, getTenantListApi } from '@/api';
//分页
const total = ref(0);
const loading = ref(false);
const reqForm = reactive({
  merchantGuid: '',
  pageSize: 10,
  page: 1,
});
let goodsList = ref([]);
let tenantList: any = ref([]);
//获取产品列表
const getList = async () => {
  loading.value = true;
  let goodsRes = await getCardListApi(reqForm);
  loading.value = false;
  total.value = goodsRes.data.total;
  goodsList.value = goodsRes.data.data;
};
//获取商户列表
const getTenantList = async () => {
  let res = await getTenantListApi();
  tenantList.value = res.data;
};
const handlePageChang = async (page) => {
  reqForm.page = page;
  getList();
};
//搜索
const onSearch = () => {
  getList();
};
const onViewLink = (item) => {
  if (item.length > 0) {
    window.open(item, '_blank');
  }
};
getList();
getTenantList();
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main v-loading="loading">
        <el-table :data="goodsList" border style="width: 100%">
          <el-table-column prop="sysId" label="分身Id" width="80" />
          <el-table-column prop="separationInfo.baseRealName" label="分身昵称" width="120" />
          <el-table-column prop="separationInfo.avatar" label="分身头像" width="100">
            <template #default="scope">
              <el-image :src="scope.row.separationInfo.avatar"
                style="width: 50px; height: 50px; border-radius: 50%"></el-image>
            </template>
          </el-table-column>
          <el-table-column prop="separationInfo.baseCompany" label="公司" />
          <el-table-column prop="separationInfo.baseJobPosition" label="职位" />
          <el-table-column prop="separationInfo.contactPhone" label="电话" />
          <el-table-column prop="separationInfo.contactWxNumber" label="微信号" />
          <el-table-column prop="separationInfo.contactWxImg" label="微信码">
            <template #default="scope">
              <el-image :src="scope.row.separationInfo.contactWxImg"
                style="width: 50px; height: 50px; border-radius: 50%"></el-image>
            </template>
          </el-table-column>
          <el-table-column prop="separationInfo.video" :show-overflow-tooltip="true" label="视频" width="100">
            <template #default="scope">
              <div @click="onViewLink(scope.row.separationInfo.video)"
                :class="['video-link', { active: scope.row.separationInfo.video }]">
                {{ scope.row.separationInfo.video ? '点击查看' : '' }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="separationInfo.resumeInfo" label="履历" :show-overflow-tooltip="true" />
        </el-table>
      </el-main>
      <el-footer>
        <el-pagination background layout="prev,pager, next" :total="total" :current-page="reqForm.page"
          @current-change="handlePageChang" />
      </el-footer>
    </el-container>
  </div>
</template>

<style scoped lang="scss">
.video-link {
  cursor: pointer;

  &.active {
    color: rgb(31, 25, 221);
  }
}
</style>
