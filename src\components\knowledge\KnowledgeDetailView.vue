<script setup lang="ts">
import { getKnowledgeFileListApi, delKnowledgeFileApi, addKnowledgeFileApi, uploadFileApi } from '@/api';
import type { FormInstance, FormRules, UploadInstance, UploadProps, UploadRawFile } from 'element-plus';
import { genFileId } from 'element-plus';
import { useRoute, useRouter } from 'vue-router';
const route = useRoute();
const router = useRouter();
const guid = route.query.guid;
const dialogLoading = ref(false);
const loading = ref(false);
const dialogIsShow = ref(false);
const isSubmit = ref(true);
const uploadRef = ref<UploadInstance>();
const onAddScene = () => {
  dialogIsShow.value = true;
};
let fileList = ref([]);
const testfileList = ref([]);
const total = ref(0);
const knowLedgeFileReq = reactive({
  guid: guid,
  pageSize: 10,
  page: 1,
});

const getKnowledgeFileList = async () => {
  let res = await getKnowledgeFileListApi(knowLedgeFileReq);
  fileList.value = res.data.data;
  total.value = res.data.total;
  console.log(res, '文档列表接口响应');
};
getKnowledgeFileList();
const uploadReq = reactive({
  guid: '',
  fileName: '',
  fileUrl: '',
});
const addForm = ref<FormInstance>();
const rules = reactive<FormRules>({
  fileName: [{ required: true, message: '请输入文档名', trigger: 'change' }],
  fileUrl: [{ required: true, message: '请上传文档', trigger: 'change' }],
});

const beforeFileUpload: UploadProps['beforeUpload'] = (file) => {
  const fileName = file!.name;
  let fileExtension = '';
  let arr: string[] = [];
  arr = fileName.split('.');
  if (Array.isArray(arr)) {
    fileExtension = arr.pop()!.toLowerCase();
  }
  const allowedFileTypes = [
    'txt',
    'md',
    'pdf',
    'html',
    'xlsx',
    'xls',
    'docx',
    'csv',
    'eml',
    'msg',
    'pptx',
    'ppt',
    'xml',
    'epub',
  ];
  if (file.size / 1024 / 1024 > 15) {
    ElMessage.warning('文件大小必须小于15MB');
    return false;
  }
  if (!allowedFileTypes.includes(fileExtension)) {
    ElMessage.warning('不支持上传该文件类型');
    return false;
  }
};
const onUpload = async (file) => {
  const formData = new FormData();
  // 添加要上传的文件名和文件对象,key就是你上传文件的字段名
  formData.append('file', file.file);
  dialogLoading.value = true;
  let res = await uploadFileApi(formData);
  dialogLoading.value = false;
  uploadReq.fileName = file.file.name;
  uploadReq.fileUrl = res.data;
};
const onRemove = () => {
  uploadReq.fileName = '';
  uploadReq.fileUrl = '';
  isSubmit.value = true;
};
const onSuccess = () => {
  isSubmit.value = false;
};
const onError = () => {
  dialogLoading.value = false;
  isSubmit.value = true;
};
const handleExceed: UploadProps['onExceed'] = (files) => {
  uploadRef.value!.clearFiles();
  const file = files[0] as UploadRawFile;
  file.uid = genFileId();
  uploadRef.value!.handleStart(file);
};
const onSave = async (formEl) => {
  formEl.validate(async (valid) => {
    if (!valid) {
      return;
    }
    try {
      uploadReq.guid = route.query.guid as string;
      await addKnowledgeFileApi(uploadReq);
      ElMessage.success('新增成功');
      nextTick(() => {
        formEl.resetFields();
        uploadRef.value?.clearFiles();
        testfileList.value = [];
        dialogIsShow.value = false;
      });
      getKnowledgeFileList();
    } catch (error: any) {
      ElMessage.error(error);
      throw new Error(error);
    }
  });
};
const handleDelete = async (item) => {
  await delKnowledgeFileApi({ fileGuid: item.guid });
  ElMessage.success('删除成功');
  getKnowledgeFileList();
};
const handlePageChang = async (page) => {
  knowLedgeFileReq.page = page;
  getKnowledgeFileList();
};
const onLook = (item) => {
  router.push({
    name: 'knowledgefiledetail',
    query: {
      fileGuid: item.guid,
    },
  });
};
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main>
        <div class="header-box">
          <el-form :inline="true" class="demo-form-inline">
            <el-form-item><el-button type="primary" @click="onAddScene">新增知识库文档</el-button></el-form-item>
            <!-- <el-form-item><el-button type="primary" @click="onSearchTest">知识库召回测试</el-button></el-form-item> -->
          </el-form>
        </div>
        <el-table v-loading="loading" :data="fileList" border style="width: 100%">
          <el-table-column prop="sysId" label="id" width="80" />
          <el-table-column prop="fileName" label="文档名称" />
          <el-table-column prop="modifyTime" label="上传日期" />
          <el-table-column label="操作" width="300">
            <template #default="scope">
              <el-button size="small" type="primary" @click="onLook(scope.row)">查看文档片段</el-button>
              <el-popconfirm confirm-button-text="确认" cancel-button-text="取消" icon-color="red" title="是否删除该文档?"
                @confirm="handleDelete(scope.row)">
                <template #reference>
                  <el-button size="small" type="danger">删除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </el-main>
      <el-footer>
        <el-pagination background layout="prev,pager, next" :total="total" :current-page="knowLedgeFileReq.page"
          @current-change="handlePageChang" />
      </el-footer>
    </el-container>
    <el-dialog v-model="dialogIsShow" title="上传文档" width="600" draggable>
      <el-form ref="addForm" :model="uploadReq" class="demo-form-inline" :rules="rules" label-width="100px">
        <el-form-item label="上传文档" prop="fileUrl" v-loading="dialogLoading">
          <el-upload ref="upload" class="upload-box" v-model:file-list="testfileList" drag :limit="1"
            :http-request="(params) => onUpload(params)" :on-remove="onRemove" :on-success="onSuccess"
            :on-error="onError" :on-exceed="handleExceed" :before-upload="beforeFileUpload">
            <el-icon class="el-icon--upload"><i-ep-upload-filled /></el-icon>
            <div class="el-upload__text">将文件拖放到此处或单击上传。</div>
            <template #tip>
              <div class="el-upload__tip">
                支持TXT、 MARKDOWN、 PDF、 HTML、 XLSX、 XLS、 DOCX、 CSV、 EML、 MSG、 PPTX、 PPT、 XML、
                EPUB,一个文件上传
              </div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item label="文档名称" prop="fileName">
          <el-input v-model="uploadReq.fileName" placeholder="文档名称" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :disabled="isSubmit" @click="onSave(addForm)">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss"></style>
