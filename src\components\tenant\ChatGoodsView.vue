<script setup lang="ts">
import { getChatsGoodsApi, createTenantChatApi, editTenantChatApi, delTenantChatApi } from '@/api';
import type { FormInstance, FormRules } from 'element-plus';
import { useRoute } from 'vue-router';

const route = useRoute();
//分页
const loading = ref(false);
const addForm = ref<FormInstance>();
let dialogVisible = ref(false);
let dialogtype = ref('');

const reqForm = reactive({
  merchantGuid: '',
});
const reqAddFrm = reactive({
  merchantGuid: '',
  goodsGuid: '',
  goodsName: '',
  goodsDesc: '',
  chatCount: 1,
  price: '',
});
let goodsList = ref([]);

const rules = reactive<FormRules>({
  goodsName: [{ required: true, message: '请输入商品名', trigger: 'blur' }],
  goodsDesc: [{ required: true, message: '请输入描述', trigger: 'blur' }],
  chatCount: [{ required: true, message: '输入点数', trigger: 'blur' }],
  price: [{ required: true, message: '请输入价格', trigger: 'blur' }],
});

//获取产品列表
const getChatGoods = async () => {
  loading.value = true;
  reqForm.merchantGuid = route.query.guid as string;
  let goodsRes = await getChatsGoodsApi(reqForm);
  loading.value = false;
  goodsList.value = goodsRes.data;
};
//新增
const onAddBanner = () => {
  dialogVisible.value = true;
  dialogtype.value = 'add';
};
const onEdit = (item) => {
  reqAddFrm.goodsGuid = item.guid;
  reqAddFrm.goodsName = item.goodsName;
  reqAddFrm.goodsDesc = item.goodsDesc;
  reqAddFrm.chatCount = item.chatCount;
  reqAddFrm.price = item.price;
  dialogVisible.value = true;
  dialogtype.value = 'edit';
};
const onDelete = async (item) => {
  await delTenantChatApi({ goodsGuid: item.guid });
  ElMessage.success('删除成功');
  getChatGoods();
};
const onSubmit = (formEl) => {
  reqAddFrm.merchantGuid = route.query.guid as string;
  formEl.validate(async (valid) => {
    if (!valid) {
      return;
    }
    try {
      if (dialogtype.value === 'add') {
        await createTenantChatApi(reqAddFrm);
        ElMessage.success('新增成功');
      } else if (dialogtype.value === 'edit') {
        await editTenantChatApi(reqAddFrm);
        ElMessage.success('修改成功');
      }
      formEl.resetFields();
      dialogVisible.value = false;
      getChatGoods();
    } catch (error: any) {
      ElMessage.error(error);
      throw new Error(error);
    }
  });
};
onActivated(() => {
  getChatGoods();
});
getChatGoods();
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main v-loading="loading">
        <div class="header-box">
          <el-button type="primary" @click="onAddBanner">新增商品</el-button>
        </div>
        <el-table :data="goodsList" border style="width: 100%">
          <el-table-column prop="sysId" label="商品Id" width="80" />
          <el-table-column prop="goodsName" label="商品昵称" width="180" />
          <el-table-column prop="goodsDesc" label="商品描述" />
          <el-table-column prop="chatCount" label="商品点数" width="100" />
          <el-table-column prop="price" label="商品价格" width="100" />
          <el-table-column label="操作">
            <template #default="scope">
              <el-button size="small" type="primary" @click="onEdit(scope.row)">编辑</el-button>
              <el-button size="small" type="danger" @click="onDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-main>
    </el-container>
    <el-dialog v-model="dialogVisible" title="修改/新增 商品" width="600px">
      <el-form ref="addForm" :model="reqAddFrm" class="demo-form-inline" label-width="100px" :rules="rules">
        <el-form-item label="商品名称" prop="goodsName">
          <el-input v-model="reqAddFrm.goodsName" placeholder="商品名称" />
        </el-form-item>
        <el-form-item label="商品描述" prop="goodsDesc">
          <el-input v-model="reqAddFrm.goodsDesc" placeholder="商品描述" />
        </el-form-item>
        <el-form-item label="商品点数" prop="chatCount">
          <el-input-number v-model="reqAddFrm.chatCount" :min="1" />
        </el-form-item>
        <el-form-item label="商品价格" prop="price">
          <el-input v-model="reqAddFrm.price" type="n" placeholder="商品价格" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit(addForm)">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.header-box {
  margin-bottom: 18px;
}
</style>
