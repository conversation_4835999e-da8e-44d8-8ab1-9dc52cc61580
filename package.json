{"name": "zqxl-admin", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "run-p type-check build-only", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@kangc/v-md-editor": "^2.3.18", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.4.0", "dayjs": "^1.11.7", "element-plus": "^2.3.3", "jshashes": "^1.0.8", "normalize.css": "^8.0.1", "pinia": "^2.0.32", "sass": "^1.62.0", "screenfull": "^6.0.2", "vue": "^3.2.47", "vue-draggable-plus": "^0.6.0", "vue-router": "^4.1.6"}, "devDependencies": {"@iconify-json/ep": "^1.1.10", "@rushstack/eslint-patch": "^1.2.0", "@types/node": "^18.16.3", "@vitejs/plugin-vue": "^4.0.0", "@vue/eslint-config-prettier": "^7.1.0", "@vue/eslint-config-typescript": "^11.0.2", "@vue/tsconfig": "^0.1.3", "eslint": "^8.34.0", "eslint-plugin-vue": "^9.9.0", "npm-run-all": "^4.1.5", "prettier": "^2.8.4", "typescript": "~4.8.4", "unplugin-auto-import": "^0.15.3", "unplugin-icons": "^0.16.1", "unplugin-vue-components": "^0.24.1", "vite": "^4.1.4", "vue-tsc": "^1.2.0"}}