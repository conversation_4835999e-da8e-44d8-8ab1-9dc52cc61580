<script setup lang="ts">
import { getFileSegmentsApi, updateFileSegmentsApi } from '@/api';
import { useRoute } from 'vue-router';
import { ElInput } from 'element-plus';
import type { FormInstance } from 'element-plus';

const route = useRoute();
const fileGuid = route.query.fileGuid;
const loading = ref(false);
const dialogIsShow = ref(false);
const editDialogIsShow = ref(false);
let fileSegments = ref([]);
const getFileSegments = async () => {
  loading.value = true;
  let res = await getFileSegmentsApi({ fileGuid });
  loading.value = false;
  fileSegments.value = res.data.data;
};
interface FileItem {
  word_count: number;
  enabled: boolean;
  content: string;
  keywords: string[];
  index_node_hash: string;
  hit_count: number;
  id: string;
}
getFileSegments();
let fileItem = reactive<FileItem>({
  word_count: 0,
  enabled: false,
  content: '',
  keywords: [],
  index_node_hash: '',
  hit_count: 0,
  id: '',
});
const onLook = (row: any) => {
  fileItem = { fileItem, ...row };
  dialogIsShow.value = true;
};
const onEdit = (row: any) => {
  fileItem = reactive(Object.assign({}, fileItem, row));
  editDialogIsShow.value = true;
};
const keywordValue = ref('');
const inputVisible = ref(false);
const InputRef = ref<InstanceType<typeof ElInput>>();

const handleClose = (tag: string) => {
  fileItem.keywords.splice(fileItem.keywords.indexOf(tag), 1);
};
const onEditKeyword = () => {
  inputVisible.value = true;
  nextTick(() => {
    InputRef.value!.input!.focus();
  });
};

const handleInputConfirm = () => {
  if (keywordValue.value) {
    fileItem.keywords.push(keywordValue.value);
  }
  inputVisible.value = false;
  keywordValue.value = '';
};
const addForm = ref<FormInstance>();
const onSave = async () => {
  let editReq = {
    fileGuid: fileGuid,
    segmentId: fileItem.id,
    segmentsData: {
      segment: fileItem,
    },
  };
  let res = await updateFileSegmentsApi(editReq);
  editDialogIsShow.value = false;
  getFileSegments();
  console.log(res, 'resresresres');
};
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main>
        <el-table v-loading="loading" :data="fileSegments" border style="width: 100%">
          <el-table-column prop="tokens" label="tokens" width="80" />
          <el-table-column prop="word_count" label="字数" width="80" />
          <!-- <el-table-column label="状态" width="80">
            <template #default="scope">
              <el-switch v-model="scope.row.enabled" disabled />
            </template>
</el-table-column> -->
          <!-- <el-table-column prop="content" :show-overflow-tooltip="true" label="文档段落" /> -->
          <el-table-column label="文档段落">
            <template #default="scope">
              <div class="content-text">{{ scope.row.content }}</div>
            </template>
          </el-table-column>
          <el-table-column label="关键字" width="300">
            <template #default="scope">
              <el-tag type="info" v-for="item in scope.row.keywords" :key="item">{{ item }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button size="small" type="primary" @click="onLook(scope.row)">查看</el-button>
              <el-button size="small" type="primary" @click="onEdit(scope.row)">编辑</el-button>
              <!-- <el-popconfirm
                confirm-button-text="确认"
                cancel-button-text="取消"
                icon-color="red"
                title="是否删除该文档?"
                @confirm="handleDelete(scope.row)">
                <template #reference>
                  <el-button size="small" type="danger">删除</el-button>
                </template>
  </el-popconfirm> -->
            </template>
          </el-table-column>
        </el-table>
      </el-main>
    </el-container>
    <el-dialog v-model="dialogIsShow" title="文档段落" width="600">
      <div class="text-paragraph-box">
        <div class="content">
          <!-- {{ fileItem.content }} -->
          <el-input :input-style="{ width: '100%' }" v-model="fileItem.content" :rows="20" disabled type="textarea"
            resize="none" />
        </div>
        <div class="keywords">
          <div class="title"><el-text size="small">关键词</el-text></div>
          <div class="item-box">
            <el-tag v-for="(item, index) in fileItem.keywords" :key="index">{{ item }}</el-tag>
          </div>
        </div>
        <div class="detail">
          <el-text class="mx-1" size="small">{{ fileItem.word_count }}字符</el-text>
          <el-text class="mx-1" size="small">{{ fileItem.hit_count }}召回次数</el-text>
          <el-text class="mx-3" size="small" truncated>向量哈希:{{ fileItem.index_node_hash }}</el-text>
          <div>
            <el-text class="status" size="small">{{ fileItem.enabled ? '已启用' : '未启用' }}</el-text><el-switch
              v-model="fileItem.enabled" disabled />
          </div>
        </div>
      </div>
    </el-dialog>
    <el-dialog v-model="editDialogIsShow" title="编辑段落" width="600">
      <div class="text-paragraph-box">
        <el-form ref="addForm" :model="fileItem" class="demo-form-inline" label-width="0px">
          <el-form-item prop="content">
            <el-input :input-style="{ width: '100%' }" v-model="fileItem.content" :rows="20" type="textarea"
              resize="none" />
          </el-form-item>
          <el-form-item prop="keywords">
            <div class="keywords">
              <div class="title"><el-text size="small">关键词</el-text></div>
              <div class="item-box">
                <el-tag v-for="(item, index) in fileItem.keywords" :key="index" closable :disable-transitions="false"
                  @close="handleClose(item)">
                  {{ item }}
                </el-tag>
                <el-input v-if="inputVisible" ref="InputRef" v-model="keywordValue" class="w-20" size="small"
                  @keyup.enter="handleInputConfirm" @blur="handleInputConfirm" />
                <el-button v-else class="button-new-tag" size="small" @click="onEditKeyword"> + 新增 </el-button>
              </div>
            </div>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSave()">保存</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.content-text {
  font-size: 14px;
  width: 100%;
  margin-top: 10px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
  /* 设置最大显示行数为4 */
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4em;
}

:deep(.el-textarea.is-disabled .el-textarea__inner) {
  background-color: inherit;
  /* 继承父元素背景色 */
  color: inherit;
  /* 继承父元素字体颜色 */
  border-color: inherit;
  /* 继承父元素边框颜色 */
  cursor: auto;
  /* 鼠标指针变为禁用状态的样式 */
}

.text-paragraph-box {
  .keywords {
    .title {
      margin-bottom: 10px;
      margin-top: 20px;
    }

    .item-box {
      display: flex;
      grid-gap: 0.5rem;
      gap: 0.5rem;
      flex-wrap: wrap;
    }
  }

  .detail {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #d7d7d7;
    display: flex;
    justify-content: space-between;

    .mx-1 {
      width: 80px;
    }

    .mx-3 {
      width: 280px;
    }

    .status {
      margin-right: 10px;
    }
  }
}

:deep(.el-dialog__header) {
  padding: 20px 20px 5px 20px;
}

:deep(.el-dialog__body) {
  padding: 20px;
}
</style>
