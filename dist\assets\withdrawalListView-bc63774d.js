import{d as X,a5 as ee,r as u,a as h,a6 as te,c as d,b as e,w as o,h as l,A as ae,bq as oe,e as re,C as le,o as n,T as E,I as M,F as c,f as _,U as w,i as v,G as se,H as ne,ah as ie,ai as de,E as C,J as pe,br as ue,s as ce,N as _e,p as me,v as he,y as we,x as ge,Y as fe,m as ye,n as ve,l as be,O as Ve,P as ke,q as Ie,Z as xe,_ as Ee}from"./index-6e8b0ade.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                     *//* empty css                  *//* empty css                    */import{_ as Me,a as Ce}from"./plus-19e9063c.js";/* empty css                 *//* empty css               *//* empty css                  *//* empty css                      *//* empty css                        *//* empty css                        */const Se={key:0},We={key:1},Fe={key:2},Ne={style:{margin:"0px"}},Le={style:{margin:"0px"}},Te={key:3,style:{margin:"0px"}},Ae={key:4,style:{margin:"0px"}},Re={class:"upload-img-box"},qe=["src"],Be={key:1,class:"upload-btn"},De=["onClick"],Ge={class:"dialog-footer"},Ue=X({__name:"withdrawalListView",setup(ze){const S=ee(),b=u(0),g=u(!1),W=h([{id:1,type:"200",name:"审核通过"},{id:2,type:"300",name:"审核拒绝"}]),m=h({merchantGuid:"",pageSize:10,page:1});let F=te(()=>S.getTenantInfo.guid),V=u([]);const f=async()=>{g.value=!0,m.merchantGuid=F.value;let r=await oe(m);g.value=!1,b.value=r.data.total,r.data.data.forEach(a=>{a.imageResult=[a.payVoucherImg]}),V.value=r.data.data},N=async r=>{m.page=r,f()},k=u(),i=h({guid:"",withdrawStatus:"200",payVoucherImg:""}),L=h({withdrawStatus:[{required:!0,message:"请选择状态",trigger:"change"}],payVoucherImg:[{required:!0,message:"请上传凭据",trigger:"change"}]}),T=r=>{let a=["image/jpg","image/png","image/jpeg"];if(!a.includes(r.type))return C.warning("当前图片仅支持格式为："+a.join(" ，")),!1},A=async r=>{const a=new FormData;a.append("img",r.file);let s=await pe(a);i.payVoucherImg=s.data},R=()=>{i.payVoucherImg=""},p=u(!1),q=r=>{i.guid=r.guid,p.value=!0},B=async r=>{r.validate(async a=>{if(a)try{await ue(i),C({message:"审核通过",type:"success"}),p.value=!1,f()}catch{}})};return f(),(r,a)=>{const s=ce,D=_e,y=me,G=he,U=we,z=ge,P=fe,j=re,O=ye,$=ve,I=be,H=Me,x=Ve,J=Ce,Y=ke,Z=Ie,K=le,Q=xe;return n(),d("div",null,[e(j,{class:"wrapper"},{default:o(()=>[E((n(),M(U,null,{default:o(()=>[e(G,{data:l(V),border:"",style:{width:"100%"}},{default:o(()=>[e(s,{prop:"user.sysId",label:"用户Id"}),e(s,{prop:"user.nickname",label:"用户昵称",width:"180"}),e(s,{prop:"user.mobile",label:"手机号",width:"120"}),e(s,{prop:"withdrawOrderNo","show-overflow-tooltip":!0,label:"提现单号",width:"120"}),e(s,{prop:"payVoucherImg",label:"打款凭据"},{default:o(t=>[e(D,{src:t.row.payVoucherImg,"initial-index":0,"preview-teleported":!0,"preview-src-list":t.row.imageResult,style:{width:"50px",height:"50px","border-radius":"50%"}},null,8,["src","preview-src-list"])]),_:1}),e(s,{prop:"bigVipGuid",label:"提现信息"},{default:o(t=>[t.row.userWithdraMethod.withdrawMethod==1?(n(),d("span",Se,"提现方式：微信")):c("",!0),t.row.userWithdraMethod.withdrawMethod==2?(n(),d("span",We,"提现方式：支付宝")):c("",!0),t.row.userWithdraMethod.withdrawMethod==3?(n(),d("span",Fe,"提现方式：银行卡")):c("",!0),_("p",Ne,"提现账号: "+w(t.row.userWithdraMethod.withdrawAccount),1),_("p",Le,"收款人: "+w(t.row.userWithdraMethod.withdrawName),1),t.row.userWithdraMethod.withdrawMethod==3?(n(),d("p",Te," 银行: "+w(t.row.userWithdraMethod.bankName),1)):c("",!0),t.row.userWithdraMethod.withdrawMethod==3?(n(),d("p",Ae," 支行: "+w(t.row.userWithdraMethod.bankBranchName),1)):c("",!0)]),_:1}),e(s,{prop:"withdrawAmount",label:"提现金额"}),e(s,{prop:"withdrawStatusText",label:"提现状态"}),e(s,{prop:"withdrawTime",label:"申请时间"}),e(s,{label:"操作"},{default:o(t=>[e(y,{size:"small",type:"primary",onClick:Pe=>q(t.row)},{default:o(()=>[v("审批")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})),[[Q,l(g)]]),e(P,null,{default:o(()=>[e(z,{background:"",layout:"prev,pager, next",total:l(b),"current-page":l(m).page,onCurrentChange:N},null,8,["total","current-page"])]),_:1})]),_:1}),e(K,{modelValue:l(p),"onUpdate:modelValue":a[3]||(a[3]=t=>ae(p)?p.value=t:null),title:"审核信息",width:"500"},{footer:o(()=>[_("div",Ge,[e(y,{onClick:a[1]||(a[1]=t=>p.value=!1)},{default:o(()=>[v("取 消")]),_:1}),e(y,{type:"primary",onClick:a[2]||(a[2]=t=>B(l(k)))},{default:o(()=>[v(" 确 认 ")]),_:1})])]),default:o(()=>[e(Z,{ref_key:"appForm",ref:k,model:l(i),rules:l(L)},{default:o(()=>[e(I,{label:"审批状态",prop:"withdrawStatus","label-width":"80px"},{default:o(()=>[e($,{modelValue:l(i).withdrawStatus,"onUpdate:modelValue":a[0]||(a[0]=t=>l(i).withdrawStatus=t)},{default:o(()=>[(n(!0),d(se,null,ne(l(W),t=>(n(),M(O,{label:t.name,value:t.type},null,8,["label","value"]))),256))]),_:1},8,["modelValue"])]),_:1}),e(I,{label:"打款凭据",prop:"payVoucherImg","label-width":"80px"},{default:o(()=>[e(Y,{ref:"uploadImgRef",class:"avatar-uploader","before-upload":T,"show-file-list":!1,"http-request":A},{default:o(()=>[_("div",Re,[l(i).payVoucherImg?(n(),d("img",{key:0,src:l(i).payVoucherImg,class:"preview-img"},null,8,qe)):(n(),d("div",Be,[e(x,{size:"30",color:"#cdd0d6"},{default:o(()=>[e(H)]),_:1})])),E(_("div",{class:"operate-box",onClick:de(R,["stop"])},[e(x,{size:"22",color:"#ffffff"},{default:o(()=>[e(J)]),_:1})],8,De),[[ie,l(i).payVoucherImg]])])]),_:1},512)]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}});const rt=Ee(Ue,[["__scopeId","data-v-a87e5fb6"]]);export{rt as default};
