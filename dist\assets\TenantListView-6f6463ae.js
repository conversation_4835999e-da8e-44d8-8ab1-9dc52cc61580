import{d as A,r as h,a as b,c as M,b as e,w as l,g as O,E as V,e as R,u as H,o as J,f as x,h as o,i as d,j as K,k as Q,l as W,m as X,n as Y,p as Z,q as ee,s as ae,t as te,v as ne,x as le,y as oe,_ as se}from"./index-6e8b0ade.js";/* empty css                      *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                  *//* empty css                     */const re={class:"search-box"},ie={class:"pagination-container"},ue=A({__name:"TenantListView",setup(de){const c=H();let f=h([]);const v=h(0),w=h(!1),t=b({merchantGuid:"",merchantName:"",status:"",pageSize:10,page:1}),p=async()=>{w.value=!0;try{let a=await O(t);a.data&&a.data.data?(f.value=a.data.data,v.value=a.data.total||a.data.data.length):(f.value=a.data,v.value=a.data.length)}catch{V.error("获取商户列表失败")}finally{w.value=!1}},z=()=>{t.page=1,p()},E=()=>{t.merchantGuid="",t.merchantName="",t.status="",t.page=1,p()},k=a=>{t.page=a,p()},S=a=>{t.pageSize=a,t.page=1,p()};let m=h(!1),_=b({merchantGuid:"",merchantName:"",merchantDesc:"",merchantChatCount:0,status:200});const N=a=>{c.push({name:"setSystem",query:{guid:a.guid,config:"show"}})},T=a=>{c.push({name:"setSystem",query:{guid:a.guid,config:"user"}})},U=a=>{c.push({name:"tenantChats",query:{guid:a.guid}})},G=a=>{c.push({name:"bannerList",query:{guid:a.guid}})},L=a=>{c.push({name:"setSystem",query:{guid:a.guid,config:"tools_show"}})},q=async a=>{_=Object.assign(_,a),_.merchantGuid=a.guid;try{m.value=!0,await K(_),V.success("修改成功"),m.value=!1}catch(s){throw m.value=!1,new Error(s)}};return p(),(a,s)=>{const y=Q,g=W,C=X,I=Y,r=Z,$=ee,i=ae,B=te,P=ne,D=le,F=oe,j=R;return J(),M("div",null,[e(j,{class:"wrapper"},{default:l(()=>[e(F,null,{default:l(()=>[x("div",re,[e($,{inline:!0,class:"search-form"},{default:l(()=>[e(g,{label:"GUID"},{default:l(()=>[e(y,{modelValue:o(t).merchantGuid,"onUpdate:modelValue":s[0]||(s[0]=n=>o(t).merchantGuid=n),placeholder:"请输入GUID",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(g,{label:"商户名称"},{default:l(()=>[e(y,{modelValue:o(t).merchantName,"onUpdate:modelValue":s[1]||(s[1]=n=>o(t).merchantName=n),placeholder:"请输入商户名称",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(g,{label:"商家状态"},{default:l(()=>[e(I,{modelValue:o(t).status,"onUpdate:modelValue":s[2]||(s[2]=n=>o(t).status=n),placeholder:"请选择状态",clearable:"",style:{width:"150px"}},{default:l(()=>[e(C,{label:"启用",value:200}),e(C,{label:"禁用",value:400})]),_:1},8,["modelValue"])]),_:1}),e(g,null,{default:l(()=>[e(r,{type:"primary",onClick:z},{default:l(()=>[d("搜索")]),_:1}),e(r,{onClick:E},{default:l(()=>[d("重置")]),_:1})]),_:1})]),_:1})]),e(P,{data:o(f),border:"",style:{width:"100%"}},{default:l(()=>[e(i,{prop:"sysId",label:"商户Id",width:"80"}),e(i,{prop:"guid",label:"guid",width:"320"}),e(i,{prop:"merchantName",label:"商户名称",width:"180"}),e(i,{prop:"merchantDesc","show-overflow-tooltip":!0,label:"商户备注",width:"200"}),e(i,{prop:"createTime",label:"注册时间",width:"200"}),e(i,{prop:"status",label:"商家状态",width:"160"},{default:l(n=>[e(B,{modelValue:n.row.status,"onUpdate:modelValue":u=>n.row.status=u,"active-text":"启用","inactive-text":"禁用","active-value":200,"inactive-value":400,loading:o(m),onChange:u=>q(n.row)},null,8,["modelValue","onUpdate:modelValue","loading","onChange"])]),_:1}),e(i,{label:"操作"},{default:l(n=>[e(r,{size:"small",type:"primary",onClick:u=>N(n.row)},{default:l(()=>[d("商家装修")]),_:2},1032,["onClick"]),e(r,{size:"small",type:"primary",onClick:u=>U(n.row)},{default:l(()=>[d("点数商品")]),_:2},1032,["onClick"]),e(r,{size:"small",type:"primary",onClick:u=>T(n.row)},{default:l(()=>[d("用户设置")]),_:2},1032,["onClick"]),e(r,{size:"small",type:"primary",onClick:u=>G(n.row)},{default:l(()=>[d("banner设置")]),_:2},1032,["onClick"]),e(r,{size:"small",type:"primary",onClick:u=>L(n.row)},{default:l(()=>[d("导航页设置")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),x("div",ie,[e(D,{"current-page":o(t).page,"onUpdate:currentPage":s[3]||(s[3]=n=>o(t).page=n),"page-size":o(t).pageSize,"onUpdate:pageSize":s[4]||(s[4]=n=>o(t).pageSize=n),"page-sizes":[10,20,50,100],total:o(v),layout:"total, sizes, prev, pager, next, jumper","prev-text":"上一页","next-text":"下一页",onSizeChange:S,onCurrentChange:k},null,8,["current-page","page-size","total"])])]),_:1})]),_:1})])}}});const we=se(ue,[["__scopeId","data-v-ee839995"]]);export{we as default};
