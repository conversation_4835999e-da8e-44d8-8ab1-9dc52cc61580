import{d as z,r as i,a as f,c as p,b as t,w as s,S as G,aW as R,e as q,o as r,f as v,h as o,G as A,H as D,aC as M,U,i as H,T as O,I as W,l as Y,p as Z,q as $,s as j,N as J,v as K,y as Q,x as X,Y as ee,Z as te,_ as ae}from"./index-6e8b0ade.js";/* empty css                   *//* empty css                      *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                        *//* empty css                     */const se={class:"header-box"},oe={class:"mode-box"},ne=["onClick"],le=z({__name:"PosterListView",setup(ie){let b=i([]);const y=async()=>{let e=await G();b.value=e.data,n.merchantGuid=e.data[0].guid,d()},n=f({merchantGuid:"",status:"",page:1,pageSize:9}),u=i(0),m=i([]);let c=i(!1);const d=async()=>{c.value=!0;let e=await R(n);u.value=e.data.total,e.data.data.forEach(a=>{switch(a.imageResult.length===0?a.path="":a.path=a.imageResult[0],a.orderStatus){case"success":a.status="执行成功";break;case"fail":a.status="执行失败";break;case"wait":a.status="等待执行";break;case"doing":a.status="正在执行";break}}),m.value=e.data.data,c.value=!1},w=async e=>{n.page=e,d()};let g=i("");const x=f([{id:0,type:"wait",name:"等待执行"},{id:1,type:"doing",name:"正在执行"},{id:2,type:"fail",name:"执行失败"},{id:3,type:"success",name:"执行成功"}]),C=e=>{g.value=e,n.status=e},k=()=>{d()};return y(),(e,a)=>{const h=Y,E=Z,L=$,_=j,I=J,P=K,S=Q,T=X,V=ee,B=q,F=te;return r(),p("div",null,[t(B,{class:"wrapper"},{default:s(()=>[t(S,null,{default:s(()=>[v("div",se,[t(L,{inline:!0,model:o(n),class:"demo-form-inline"},{default:s(()=>[t(h,{label:"场景类型"},{default:s(()=>[v("div",oe,[(r(!0),p(A,null,D(o(x),(l,N)=>(r(),p("div",{class:M(["item",{active:o(g)===l.type}]),key:N,onClick:re=>C(l.type)},U(l.name),11,ne))),128))])]),_:1}),t(h,null,{default:s(()=>[t(E,{type:"primary",onClick:k},{default:s(()=>[H("搜索")]),_:1})]),_:1})]),_:1},8,["model"])]),O((r(),W(P,{data:o(m),border:"",style:{width:"100%"}},{default:s(()=>[t(_,{prop:"sysId",label:"id",width:"80"}),t(_,{prop:"path",label:"图片",width:"100"},{default:s(l=>[t(I,{src:l.row.posterImgUrl,"initial-index":0,"z-index":9,"preview-teleported":!0,style:{width:"50px",height:"50px"}},null,8,["src"])]),_:1}),t(_,{prop:"status",label:"状态",width:"150"})]),_:1},8,["data"])),[[F,o(c)]])]),_:1}),t(V,null,{default:s(()=>[t(T,{background:"",layout:"prev,pager, next",total:o(u),"current-page":o(n).page,onCurrentChange:w},null,8,["total","current-page"])]),_:1})]),_:1})])}}});const ve=ae(le,[["__scopeId","data-v-e68abc23"]]);export{ve as default};
