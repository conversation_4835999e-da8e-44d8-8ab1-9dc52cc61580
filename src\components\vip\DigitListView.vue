<script setup lang="ts">
import { getChannelListApi, addChannnlApi } from '@/api';
import { useAdminCommonStore } from '@/stores/adminCommon';
import type { FormInstance, FormRules } from 'element-plus';
const store = useAdminCommonStore();
//分页
const total = ref(0);
const loading = ref(false);
let dialogVisible = ref(false);
const addForm = ref<FormInstance>();
const reqForm = reactive({
  merchantGuid: '',
  parentUid: '',
  mobile: '',
  pageSize: 10,
  page: 1,
});
const addChannelReq = reactive({
  merchantGuid: '',
  mobile: '',
});
let merchantGuid = computed(() => {
  return store.getTenantInfo.guid;
});
const rules = reactive<FormRules>({
  mobile: [
    { required: true, message: '请输入正确手机号码', trigger: 'blur' },
    { pattern: /^1\d{10}$/, message: '手机号必须是11位数字', trigger: 'blur' },
  ],
});
let goodsList = ref([]);
//获取数字人列表
const getList = async () => {
  loading.value = true;
  reqForm.merchantGuid = merchantGuid.value;
  let goodsRes = await getChannelListApi(reqForm);
  loading.value = false;
  total.value = goodsRes.data.total;
  goodsList.value = goodsRes.data.data;
};
const handlePageChang = async (page) => {
  reqForm.page = page;
  getList();
};
//搜索
const onSearch = () => {
  getList();
};
const onAdd = () => {
  dialogVisible.value = true;
  addChannelReq.merchantGuid = merchantGuid.value;
};
const onAddSave = async (formEl) => {
  formEl.validate(async (valid) => {
    if (!valid) {
      return;
    }
    loading.value = true;
    try {
      await addChannnlApi(addChannelReq);
      ElMessage.success('新增成功');
      dialogVisible.value = false;
      loading.value = false;
      formEl.resetFields();
      getList();
    } catch (error: any) {
      ElMessage.error(error);
      loading.value = false;
      throw new Error(error);
    }
  });
};
getList();
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main v-loading="loading">
        <div class="hearder-box">
          <el-form :inline="true" :model="reqForm" class="demo-form-inline">
            <el-form-item label="手机号码">
              <el-input v-model="reqForm.mobile" placeholder="手机号码" />
            </el-form-item>
            <el-form-item label="上级用户Id">
              <el-input v-model="reqForm.parentUid" placeholder="上级用户Id" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSearch">搜索</el-button>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onAdd">新增</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table :data="goodsList" border style="width: 100%">
          <el-table-column prop="platformUserSysId" label="用户Id" width="80" />
          <el-table-column prop="channelNickname" label="数字人昵称" width="180" />
          <el-table-column prop="userNickname" label="用户昵称" width="180" />
          <el-table-column prop="channelLogoImg" label="数字人头像" width="120">
            <template #default="scope">
              <el-image :src="scope.row.channelLogoImg"
                style="width: 50px; height: 50px; border-radius: 50%"></el-image>
            </template>
          </el-table-column>
          <el-table-column prop="mobile" label="手机号码" width="120" />
          <el-table-column prop="modifyTime" label="支付时间" />
        </el-table>
      </el-main>
      <el-footer>
        <el-pagination background layout="prev,pager, next" :total="total" :current-page="reqForm.page"
          @current-change="handlePageChang" />
      </el-footer>
    </el-container>
    <el-dialog v-model="dialogVisible" title="新增数字人" width="500px">
      <el-form ref="addForm" :model="addChannelReq" class="demo-form-inline" label-width="100px" :rules="rules">
        <el-form-item label="手机号码" prop="mobile">
          <el-input v-model="addChannelReq.mobile" placeholder="手机号码" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onAddSave(addForm)">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.add-point-box {
  display: flex;
  justify-content: center;
}
</style>
