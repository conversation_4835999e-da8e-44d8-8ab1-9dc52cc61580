import { defineStore } from 'pinia';
import { isEmptyObject } from '@/utils/common';
interface AdminInfo {
  guid: string;
  loginTime: number;
  mobile: string;
  userId: number;
  userName: string;
}
export const useAdminCommonStore = defineStore('adminCommon', {
  state: () => {
    return {
      // 所有这些属性都将自动推断其类型
      isCollapse: false,
      token: '',
      secret: '',
      adminInfo: {},
      tenantInfo: {},
      merchantGuid: '',
    };
  },
  getters: {
    getToken(state) {
      return () => {
        //此写法是为了不让getter缓存
        return state.token || window.localStorage.getItem('token') || '';
      };
    },
    getSecret(state) {
      return () => {
        return state.secret || window.localStorage.getItem('secret') || '';
      };
    },
    getAdminInfo(state) {
      let adminInfo: AdminInfo = {
        guid: '',
        loginTime: 0,
        mobile: '',
        userId: 0,
        userName: '',
      };
      const info = window.localStorage.getItem('admin') || '';
      if (!isEmptyObject(state.adminInfo)) {
        adminInfo = { ...adminInfo, ...state.adminInfo };
      } else if (info.length > 0) {
        const objInfo = JSON.parse(info);
        adminInfo = { ...adminInfo, ...objInfo };
      }
      return adminInfo;
    },
    getTenantInfo(state) {
      let tenantInfo = {
        guid: '',
        merchantChatCount: 0,
        merchantDesc: '',
        merchantName: '',
      };
      const info = window.localStorage.getItem('tenant') || '';
      if (!isEmptyObject(state.tenantInfo)) {
        tenantInfo = { ...tenantInfo, ...state.adminInfo };
      } else if (info.length > 0) {
        const objInfo = JSON.parse(info);
        tenantInfo = { ...tenantInfo, ...objInfo };
      }
      return tenantInfo;
    },
  },
  actions: {
    changeSideWidth() {
      this.isCollapse = !this.isCollapse;
    },
    saveLoginInfo(loginInfo) {
      this.token = loginInfo.token;
      window.localStorage.setItem('token', loginInfo.token);
      this.secret = loginInfo.secret;
      window.localStorage.setItem('secret', loginInfo.secret);
    },
    saveAdminInfo(adminInfo) {
      this.adminInfo = adminInfo.adminUserInfo;
      window.localStorage.setItem('admin', JSON.stringify(adminInfo.adminUserInfo));
    },
    saveTenantInfo(tenantInfo) {
      this.tenantInfo = tenantInfo;
      window.localStorage.setItem('tenant', JSON.stringify(tenantInfo));
    },
    outLogin() {
      window.localStorage.setItem('token', '');
      window.localStorage.setItem('secret', '');
      this.token = '';
      this.secret = '';
    },
  },
});
