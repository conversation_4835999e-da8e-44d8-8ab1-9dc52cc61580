import{d as O,a5 as z,r,a6 as P,a as E,by as W,c as H,b as e,w as t,a8 as J,E as i,b_ as Q,e as X,C as Y,o as C,f as I,i as c,T as ee,I as te,U as ae,ax as T,b$ as le,W as se,c0 as oe,c1 as ne,c2 as re,p as ie,l as ue,q as de,s as ce,ae as pe,v as me,y as _e,k as fe,t as ve,c3 as ye,Z as ge,_ as he}from"./index-6e8b0ade.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                        *//* empty css                  *//* empty css                 *//* empty css               *//* empty css                     */const be={class:"role-container"},Ie={class:"header-box"},ke={class:"permission-tree-container"},we={class:"dialog-footer"},Ve=O({__name:"RoleListView",setup(xe){const N=z(),k=r([]),f=r(!1),v=r([]),u=r(),d=r(!1),y=r(""),g=r(),m=r("add");P(()=>N.getTenantInfo.guid);const l=E({sysId:null,roleName:"",roleIntro:"",status:1,merchantGuid:"",isDefaultForZhanhui:0,permissions:[]}),R={roleName:[{required:!0,message:"请输入角色名称",trigger:"blur"}],roleIntro:[{required:!0,message:"请输入角色描述",trigger:"blur"}],merchantGuid:[{required:!0,message:"请输入商户标识",trigger:"blur"}]},D=E({merchantGuid:"",pageSize:100,page:1}),h=async()=>{f.value=!0;try{const s=await J(D);console.log("res",s),k.value=s.data.list}catch{i.error("获取角色列表失败")}finally{f.value=!1}},w=async()=>{try{const s=await Q({});v.value=s.data}catch{i.error("获取权限菜单失败")}},L=()=>{m.value="add",y.value="新增角色",Object.assign(l,{sysId:null,roleName:"",roleIntro:"",status:1,merchantGuid:"",isDefaultForZhanhui:0,permissions:[]}),T(()=>{u.value&&u.value.setCheckedKeys([])}),d.value=!0},b=r(0),A=async s=>{b.value=s.sysId,m.value="edit",y.value="编辑角色";try{v.value.length===0&&await w();const n=(await le({sysId:s.sysId})).data;Object.assign(l,{sysId:n.sysId,roleName:n.roleName,roleIntro:n.roleIntro,status:n.status,merchantGuid:n.merchantGuid,isDefaultForZhanhui:n.isDefaultForZhanhui,permissions:n.permissions||[]}),d.value=!0,await T(),u.value&&l.permissions&&u.value.setCheckedKeys(l.permissions)}catch{i.error("获取角色详情失败")}finally{b.value=0}},B=s=>{se.confirm("确认删除该角色吗？","提示",{type:"warning"}).then(async()=>{try{await oe({sysId:s.sysId}),i.success("删除成功"),h()}catch{i.error("删除失败")}})},F=async()=>{g.value&&await g.value.validate(async s=>{if(s)try{u.value&&(l.permissions=u.value.getCheckedKeys()),m.value==="edit"?(await ne(l),i.success("更新成功")):(await re(l),i.success("创建成功")),d.value=!1,h()}catch{i.error(m.value==="edit"?"更新失败":"创建失败")}})};return W(()=>{h(),w()}),(s,o)=>{const n=ie,p=ue,V=de,_=ce,G=pe,M=me,q=_e,S=X,x=fe,U=ve,Z=ye,$=Y,K=ge;return C(),H("div",be,[e(S,{class:"wrapper"},{default:t(()=>[e(q,null,{default:t(()=>[I("div",Ie,[e(V,{inline:!0,class:"demo-form-inline"},{default:t(()=>[e(p,null,{default:t(()=>[e(n,{type:"primary",onClick:L},{default:t(()=>[c("新增角色")]),_:1})]),_:1})]),_:1})]),ee((C(),te(M,{data:k.value,border:""},{default:t(()=>[e(_,{prop:"roleName",label:"角色名称"}),e(_,{prop:"roleIntro",label:"角色描述"}),e(_,{prop:"status",label:"状态"},{default:t(({row:a})=>[e(G,{type:a.status?"success":"info"},{default:t(()=>[c(ae(a.status?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),e(_,{label:"操作",width:"200"},{default:t(({row:a})=>[e(n,{type:"primary",link:"",loading:b.value===a.sysId,onClick:j=>A(a)},{default:t(()=>[c(" 编辑 ")]),_:2},1032,["loading","onClick"]),e(n,{type:"danger",link:"",onClick:j=>B(a)},{default:t(()=>[c("删除")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[K,f.value]])]),_:1})]),_:1}),e($,{title:y.value,modelValue:d.value,"onUpdate:modelValue":o[4]||(o[4]=a=>d.value=a),width:"700px"},{footer:t(()=>[I("span",we,[e(n,{onClick:o[3]||(o[3]=a=>d.value=!1)},{default:t(()=>[c("取消")]),_:1}),e(n,{type:"primary",onClick:F},{default:t(()=>[c("确定")]),_:1})])]),default:t(()=>[e(V,{ref_key:"formRef",ref:g,model:l,rules:R,"label-width":"120px"},{default:t(()=>[e(p,{label:"角色名称",prop:"roleName"},{default:t(()=>[e(x,{modelValue:l.roleName,"onUpdate:modelValue":o[0]||(o[0]=a=>l.roleName=a),placeholder:"请输入角色名称"},null,8,["modelValue"])]),_:1}),e(p,{label:"角色描述",prop:"roleIntro"},{default:t(()=>[e(x,{modelValue:l.roleIntro,"onUpdate:modelValue":o[1]||(o[1]=a=>l.roleIntro=a),placeholder:"请输入角色描述"},null,8,["modelValue"])]),_:1}),e(p,{label:"状态",prop:"status"},{default:t(()=>[e(U,{modelValue:l.status,"onUpdate:modelValue":o[2]||(o[2]=a=>l.status=a),"active-value":1,"inactive-value":0,"active-text":"启用","inactive-text":"禁用"},null,8,["modelValue"])]),_:1}),e(p,{label:"权限设置",prop:"permissions"},{default:t(()=>[I("div",ke,[e(Z,{ref_key:"permissionTree",ref:u,data:v.value,"show-checkbox":"","node-key":"sysId",props:{label:"name",children:"children"},"default-expand-all":"","expand-on-click-node":!1,"check-on-click-node":!0},null,8,["data"])])]),_:1})]),_:1},8,["model"])]),_:1},8,["title","modelValue"])])}}});const Fe=he(Ve,[["__scopeId","data-v-f22ba19d"]]);export{Fe as default};
