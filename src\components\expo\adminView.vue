<script setup lang="ts">
import { useRoute } from 'vue-router';
import {
  getZhanhuiAdminUsersApi,
  zhanhuiAdminUserDeleteApi,
  zhanhuiAdminUserCreateApi,
  adminChangeStatusApi,
} from '@/api';
import type { FormRules, FormInstance } from 'element-plus';

const route = useRoute();
//分页
const total = ref(0);
const loading = ref(false);
const reqForm = reactive({
  zhanhuiGuid: route.query.guid as string,
});
let goodsList = ref([]);
//弹窗相关
let dialogVisible = ref(false);
const addForm = ref<FormInstance>();
const addReqForm = reactive({
  zhanhuiGuid: route.query.guid as string,
  userGuid: '',
  merchantGuid: ''
});
const rules = reactive<FormRules>({
  userGuid: [{ required: true, message: '请输入邀请码', trigger: 'blur' }],
});
const logiLoading = ref(false);
//获取管理员列表
const getList = async () => {
  loading.value = true;
  let goodsRes = await getZhanhuiAdminUsersApi(reqForm);
  loading.value = false;
  goodsList.value = goodsRes.data;
};
const submitForm = async (formEl) => {
  formEl.validate(async (valid) => {
    if (!valid) {
      return;
    }
    logiLoading.value = true;
    try {
      await zhanhuiAdminUserCreateApi(addReqForm);
      ElMessage.success('新增成功');
      dialogVisible.value = false;
      logiLoading.value = false;
      formEl.resetFields();
      getList();
    } catch (error: any) {
      ElMessage.error(error);
      logiLoading.value = false;
      throw new Error(error);
    }
  });
};

const handleDelete = async (item) => {
  try {
    let guid = item.guid;
    await zhanhuiAdminUserDeleteApi({ adminGuid: guid });
    getList();
    ElMessage.success('删除成功');
  } catch (error: any) {
    ElMessage.success(error);
  }
};
// const handlePageChang = async (page: number) => {
//   reqForm.page = page;
//   getList();
// };
const onAddAdmin = () => {
  addReqForm.merchantGuid = route.query.guid as string;
  dialogVisible.value = true;
};
onMounted(() => {
  reqForm.zhanhuiGuid = route.query.guid as string;
  getList();
});
const handleChange = async (item) => {
  await adminChangeStatusApi({
    adminGuid: item.guid,
    isSuperAdmin: item.isSuperAdmin,
  });
  getList();
};
watch(() => route.query.guid, (newVal) => {
  if (newVal) {
    reqForm.zhanhuiGuid = route.query.guid as string;
    getList();
  }
});
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main v-loading="loading">
        <div class="header-box">
          <el-button type="primary" @click="onAddAdmin">新增管理员</el-button>
        </div>
        <el-table :data="goodsList" border style="width: 100%">
          <el-table-column prop="sysId" label="Id" width="80" />
          <el-table-column prop="user.guid" label="邀请码" />
          <el-table-column prop="user.nickname" label="账户名称" width="180" />
          <el-table-column label="操作">
            <template #default="scope">
              <el-popconfirm confirm-button-text="确认" cancel-button-text="取消" icon-color="red" title="是否删除该管理?"
                @confirm="handleDelete(scope.row)">
                <!-- @cancel="cancelEvent" -->
                <template #reference>
                  <el-button size="small" type="danger">删除</el-button>
                </template>
              </el-popconfirm>
              <view class="switch-box">
                <el-switch v-model="scope.row.isSuperAdmin" active-text="超级管理员" :active-value="1" :inactive-value="0"
                  inactive-text="管理员" @change="handleChange(scope.row)"></el-switch>
              </view>
            </template>
          </el-table-column>
        </el-table>
      </el-main>
    </el-container>
    <el-dialog v-model="dialogVisible" title="新增/修改 管理员" width="600px">
      <el-form ref="addForm" :model="addReqForm" :rules="rules">
        <el-form-item label="邀请码" prop="userGuid">
          <el-input v-model="addReqForm.userGuid" autocomplete="off" placeholder="请输入邀请码" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitForm(addForm)" :loading="logiLoading">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.header-box {
  margin-bottom: 18px;
}

.switch-box {
  margin-left: 16px;
}
</style>
