export const isEmptyObject = (obj: object): boolean => {
  return Object.keys(obj).length === 0;
};

export const getUrlBeforeParms = function (url: string) {
  console.log(url.split('?'), 'urlStrurlStrurlStr');
  // 通过 ? 分割获取后面的参数字符串
  const urlStr = url.split('?')[0];

  if (!urlStr) return {};
  // 创建空对象存储参数
  const obj = {};
  // 再通过 & 将每一个参数单独分割出来
  const paramsArr = urlStr.split('&');
  for (let i = 0, len = paramsArr.length; i < len; i++) {
    // 再通过 = 将每一个参数分割为 key:value 的形式
    const arr = paramsArr[i].split('=');
    obj[arr[0]] = arr[1];
  }
  return obj;
};
export const getUrlAfterParms = function (url: string) {
  // 通过 ? 分割获取后面的参数字符串
  const urlStr = url.split('?')[1];

  if (!urlStr) return {};
  // 创建空对象存储参数
  const obj = {};
  // 再通过 & 将每一个参数单独分割出来
  const paramsArr = urlStr.split('&');
  for (let i = 0, len = paramsArr.length; i < len; i++) {
    // 再通过 = 将每一个参数分割为 key:value 的形式
    const arr = paramsArr[i].split('=');
    obj[arr[0]] = arr[1];
  }
  return obj;
};
