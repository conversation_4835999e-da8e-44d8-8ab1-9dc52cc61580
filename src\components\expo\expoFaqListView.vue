<script setup lang="ts">
import { VueDraggable } from 'vue-draggable-plus';
import '@wangeditor/editor/dist/css/style.css';
import { expoFaqListsApi, uploadImg, uploadVideo, faqCreateApi, faqDeleteApi, faqUpdateApi } from '@/api';
import { useRoute } from 'vue-router';
import type { UploadInstance, UploadProps, FormInstance, FormRules } from 'element-plus';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
const route = useRoute();
//分页
const loading = ref(false);
const addForm = ref<FormInstance>();
const editorMode = ref('default');
// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef();
const toolbarConfig = {
  excludeKeys: ['group-image', 'insertVideo', 'group-video'],
};
const modeList = reactive([
  {
    id: 0,
    type: 1,
    name: '首页滚动',
  },
  {
    id: 1,
    type: 2,
    name: '轮播图位置',
  },
  {
    id: 2,
    type: 3,
    name: '欢迎语下方常见问题',
  },
  {
    id: 3,
    type: 4,
    name: '底部常用工具',
  },
]);
const typeList = reactive([
  {
    id: 0,
    type: 1,
    name: '富文本',
  },
  {
    id: 1,
    type: 2,
    name: '图片',
  },
  {
    id: 2,
    type: 3,
    name: '链接',
  },
  {
    id: 3,
    type: 4,
    name: '视频',
  },
  {
    id: 4,
    type: 5,
    name: '小程序',
  },
]);
import index_recommend from '../../static/icons/<EMAIL>';
import index_notice from '../../static/icons/<EMAIL>';
import index_msg from '../../static/icons/<EMAIL>';
import index_hot from '../../static/icons/<EMAIL>';
import msg_msg from '../../static/icons/<EMAIL>';
import msg_other from '../../static/icons/<EMAIL>';
import msg_search from '../../static/icons/<EMAIL>';
const indexIcons = [
  {
    name: '推荐',
    msg: 'recommend',
    path: index_recommend,
  },
  {
    name: '通知',
    msg: 'notice',
    path: index_notice,
  },
  {
    name: '消息',
    msg: 'msg',
    path: index_msg,
  },
  {
    name: '热点',
    msg: 'hot',
    path: index_hot,
  },
];
const msgIcons = [
  {
    name: '消息',
    msg: 'msg',
    path: msg_msg,
  },
  {
    name: '其他',
    msg: 'other',
    path: msg_other,
  },
  {
    name: '搜索',
    msg: 'search',
    path: msg_search,
  },
];

const editorConfig = { MENU_CONF: {} };
editorConfig.MENU_CONF!['uploadImage'] = {
  // 自定义上传图片 方法
  customInsert: (res, insertFn) => {
    insertFn(res.data, '富文本插图', res.data);
  },
  server: 'https://ai-api.deepcity.cn/merchant/admin.index/uploadImg',
  uploadImgMaxLength: 9,
  maxFileSize: 5 * 1024 * 1024, // 单个文件的最大体积限制，默认为 5M
  fieldName: 'img',
  timeout: 20 * 1000,
};
const total = ref(0);
const reqListForm = reactive({
  zhanhuiGuid: '',
  position: 1,
  pageSize: 10,
  page: 1,
});
let createReq = reactive({
  merchantGuid: '',
  zhanhuiGuid: '',
  position: 1,
  question: '',
  aiChatQuestion: '',
  answerType: 1,
  answer: '',
  // answerVideo: '',
  // answerImage: '',
  // answerLink: '',
  iconType: '',
  sort: 1,
  imageList: [] as string[],
});
const fromType = ref('ADD');
const dialogConfig = ref(false);
let goodsList = ref([]);
//获取问题列表
const getList = async () => {
  loading.value = true;
  let goodsRes = await expoFaqListsApi(reqListForm);
  loading.value = false;
  total.value = goodsRes.data.total;
  goodsList.value = goodsRes.data.data;
};
const handlePageChang = async (page) => {
  reqListForm.page = page;
  getList();
};
const rules = reactive<FormRules>({
  position: [{ required: true, message: '请选择问题位置', trigger: 'change' }],
  question: [{ required: true, message: '请输入问题', trigger: 'blur' }],
  aiChatQuestion: [{ required: true, message: '请输入展示问题标题', trigger: 'blur' }],
  answerType: [{ required: true, message: '请选择回答类型', trigger: 'change' }],
  answer: [{ required: true, message: '请输入回答答案', trigger: 'blur' }],
  // answerVideo: [{ required: true, message: '请上传回答视频', trigger: 'change' }],
  // answerImage: [{ required: true, message: '请上传回答图片', trigger: 'change' }],
  // answerLink: [{ required: true, message: '请输入回答答案', trigger: 'blur' }],
  iconType: [{ required: true, message: '请选择标题图标', trigger: 'change' }],
});
const uploadImgRef = ref<UploadInstance>();
const beforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
  let fileTypes = ['image/jpg', 'image/png', 'image/jpeg'];
  if (!fileTypes.includes(rawFile.type)) {
    ElMessage.warning('当前图片仅支持格式为：' + fileTypes.join(' ，'));
    return false;
  }
};

const upload = async (file) => {
  const formData = new FormData();
  // 添加要上传的文件名和文件对象,key就是你上传文件的字段名
  formData.append('img', file.file);
  let res = await uploadImg(formData);
  createReq.answer = res.data;
};
const handleRemove = () => {
  createReq.answer = '';
};
//上传多图片
const uploadList = async (file) => {
  const formData = new FormData();
  // 添加要上传的文件名和文件对象,key就是你上传文件的字段名
  formData.append('img', file.file);
  let res = await uploadImg(formData);
  createReq.imageList.push(res.data);
};
const handleListRemove = (index) => {
  createReq.imageList.splice(index, 1);
};
//上传视频
const beforeVideoUpload: UploadProps['beforeUpload'] = (file) => {
  const isLt100M = file.size / 1024 / 1024 < 100;
  if (!isLt100M) {
    ElMessage.warning('上传文件大小不能超过 100M');
    return false;
  }
  return true;
};

const handleFileChange = async (file) => {
  // 判断是否是video类型
  if (file?.file.type.startsWith('video/')) {
    const formData = new FormData();
    // 添加要上传的文件名和文件对象,key就是你上传文件的字段名
    formData.append('video', file.file);
    let res = await uploadVideo(formData);
    createReq.answer = '';
    nextTick(() => {
      createReq.answer = res.data;
    });
  } else {
    alert('请选择一个视频文件！');
  }
};

const onChangeMode = (value) => {
  reqListForm.position = value;
  getList();
};
const handleCreated = (editor) => {
  editorRef.value = editor; // 记录 editor 实例，重要！
};
const onEdit = (item) => {
  createReq = Object.assign(createReq, item);
  if (createReq.imageList === null) {
    createReq.imageList = [];
  }
  dialogConfig.value = true;
  fromType.value = 'EDIT';
};
const onAdd = () => {
  dialogConfig.value = true;
  fromType.value = 'ADD';
};
const onTypeChange = () => {
  createReq.answer = '';
  // createReq.answerImage = '';
  // createReq.answerVideo = '';
  // createReq.answerLink = '';
};
const onPositinChange = () => {
  createReq.iconType = '';
};
const onSubmit = (formEl) => {
  formEl.validate(async (valid) => {
    if (!valid) {
      return;
    }
    try {
      if (fromType.value === 'ADD') {
        await faqCreateApi(createReq);
        ElMessage.success('新增成功');
        nextTick(() => {
          dialogConfig.value = false;
          formEl.resetFields();
        });
        getList();
      } else if (fromType.value === 'EDIT') {
        await faqUpdateApi(createReq);
        ElMessage.success('修改成功');
        dialogConfig.value = true;
        getList();
      }
    } catch (error: any) {
      ElMessage.error(error);
      throw new Error(error);
    }
  });
};
const handleDelete = async (item) => {
  try {
    let guid = item.guid;
    loading.value = true;
    await faqDeleteApi({ guid });
    loading.value = false;
    getList();
    ElMessage.success('删除成功');
  } catch (error: any) {
    loading.value = false;
    ElMessage.success(error);
  }
};
onMounted(() => {
  reqListForm.zhanhuiGuid = route.query.guid as string;
  createReq.zhanhuiGuid = route.query.guid as string;
  createReq.merchantGuid = route.query.merchantGuid as string;
  getList();
});
// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (editor == null) return;
  editor.destroy();
});
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main v-loading="loading">
        <div class="hearder-box">
          <el-form :inline="true" :model="reqListForm" class="demo-form-inline">
            <el-form-item label="展示位置">
              <div class="mode-box">
                <div :class="['item', { active: reqListForm.position === item.type }]" v-for="(item, index) in modeList"
                  :key="index" @click="onChangeMode(item.type)">
                  {{ item.name }}
                </div>
              </div>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onAdd">新增</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table :data="goodsList" border style="width: 100%">
          <el-table-column prop="sysId" label="sysId" width="80" />
          <!-- <el-table-column prop="guid" label="guid" /> -->
          <el-table-column prop="question" label="问题标题" />
          <el-table-column prop="answerType" label="回答类型" width="120">
            <template #default="scope">
              <span v-if="scope.row.answerType == 1">富文本</span>
              <span v-if="scope.row.answerType == 2">图片</span>
              <span v-if="scope.row.answerType == 3">链接</span>
              <span v-if="scope.row.answerType == 4">视频</span>
              <span v-if="scope.row.answerType == 5">小程序</span>
            </template>
          </el-table-column>
          <el-table-column prop="answer" label="回答内容" show-overflow-tooltip>
            <template #default="scope">
              <template v-if="scope.row.answerType == 1">{{ scope.row.answer }}</template>
              <template v-if="scope.row.answerType == 2"><el-image style="width: 60px; height: 60px"
                  :src="scope.row.answer" /></template>
              <template v-if="scope.row.answerType == 3">
                {{ scope.row.answer }}
              </template>
              <template v-if="scope.row.answerType == 4"><video width="220" height="160" controls>
                  <source :src="scope.row.answer" type="video/mp4" />
                  <source :src="scope.row.answer" type="video/ogg" />
                  您的浏览器不支持 HTML5 video 标签。
                </video></template>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="200" />
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button size="small" type="primary" @click="onEdit(scope.row)">编辑</el-button>
              <el-popconfirm confirm-button-text="确认" cancel-button-text="取消" icon-color="red" title="是否删除该问题"
                @confirm="handleDelete(scope.row)">
                <!-- @cancel="cancelEvent" -->
                <template #reference>
                  <el-button size="small" type="danger">删除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </el-main>
      <el-footer>
        <el-pagination background layout="prev,pager, next" :total="total" :current-page="reqListForm.page"
          @current-change="handlePageChang" />
      </el-footer>
    </el-container>
    <el-dialog v-model="dialogConfig" title="创建/编辑问题" width="1000px">
      <el-form ref="addForm" :model="createReq" class="demo-form-inline" label-width="120px" :rules="rules">
        <el-form-item label="展示位置" prop="position">
          <el-select v-model="createReq.position" placeholder="请选择" @change="onPositinChange">
            <el-option :label="item.name" :value="item.type" v-for="item in modeList" :key="item.type" />
          </el-select>
        </el-form-item>
        <el-form-item label="问题" prop="question">
          <el-input v-model="createReq.question" placeholder="请输入问题" />
        </el-form-item>
        <el-form-item label="AI助力问题标题" prop="aiChatQuestion">
          <el-input v-model="createReq.aiChatQuestion" placeholder="AI助力问题标题" />
        </el-form-item>
        <el-form-item label="选择问题图标" prop="iconType" v-if="createReq.position === 1">
          <el-select v-model="createReq.iconType" placeholder="请选择">
            <el-option :label="item.name" :value="item.msg" v-for="item in indexIcons" :key="item.msg">
              <div class="from-icon-box">
                <el-image style="width: 32px; height: 32px" :src="item.path" />
                <span class="label">{{ item.name }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择问题图标" prop="iconType" v-if="createReq.position === 4">
          <el-select v-model="createReq.iconType" placeholder="请选择">
            <el-option :label="item.name" :value="item.msg" v-for="item in msgIcons" :key="item.msg">
              <div class="from-icon-box">
                <el-image style="width: 32px; height: 32px" :src="item.path" />
                <span class="label">{{ item.name }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="回答类型" prop="answerType">
          <el-select v-model="createReq.answerType" placeholder="请选择" @change="onTypeChange">
            <el-option :label="item.name" :value="item.type" v-for="item in typeList" :key="item.type" />
          </el-select>
        </el-form-item>
        <el-form-item label="回答内容" prop="answer" v-if="createReq.answerType === 1">
          <div style="border: 1px solid #ccc">
            <Toolbar style="width: 414px; border-bottom: 1px solid #ccc" :defaultConfig="toolbarConfig"
              :editor="editorRef" :mode="editorMode" />
            <Editor style="width: 414px; height: 600px; overflow-y: hidden" v-model="createReq.answer"
              :defaultConfig="editorConfig" :mode="editorMode" @onCreated="handleCreated" />
          </div>
        </el-form-item>
        <el-form-item label="回答内容图" prop="imageList" v-if="createReq.answerType === 1">
          <!-- <div class="image-list-box">
            <div class="upload-img-box" v-for="(item, index) in createReq.imageList" :key="index">
              <img :src="item" class="preview-img" />
              <div class="operate-box" @click.stop="handleListRemove(index)">
                <el-icon size="22" color="#ffffff"><i-ep-Delete /></el-icon>
              </div>
            </div>
          </div> -->
          <VueDraggable ref="draggableRef" v-model="createReq.imageList" :animation="600" easing="ease-out"
            ghostClass="ghost" draggable="ul">
            <ul v-for="(item, index) in createReq.imageList" :key="index"
              class="el-upload-list el-upload-list--picture-card">
              <li class="el-upload-list__item is-success animated">
                <el-image class="originalImg" :src="item" :preview-src-list="[item]" />
                <span class="el-upload-list__item-actions">
                  <!-- 删除    -->
                  <span class="el-upload-list__item-delete" @click="handleListRemove(index)">
                    <el-icon size="22" color="#ffffff"><i-ep-Delete /></el-icon>
                  </span>
                </span>
              </li>
            </ul>
            <el-upload ref="uploadImgRef" class="avatar-uploader" :before-upload="beforeUpload" :show-file-list="false"
              :multiple="true" :http-request="uploadList">
              <div class="upload-img-box">
                <div class="upload-btn">
                  <el-icon size="30" color="#cdd0d6"><i-ep-Plus /></el-icon>
                </div>
              </div>
            </el-upload>
          </VueDraggable>
        </el-form-item>
        <el-form-item label="回答内容" prop="answer" v-if="createReq.answerType === 2">
          <el-upload ref="uploadImgRef" class="avatar-uploader" :before-upload="beforeUpload" :show-file-list="false"
            :http-request="upload">
            <div class="upload-img-box">
              <img v-if="createReq.answer" :src="createReq.answer" class="preview-img" />
              <div class="upload-btn" v-else>
                <el-icon size="30" color="#cdd0d6"><i-ep-Plus /></el-icon>
              </div>
              <div class="operate-box" v-show="createReq.answer" @click.stop="handleRemove">
                <el-icon size="22" color="#ffffff"><i-ep-Delete /></el-icon>
              </div>
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="回答内容" prop="answer" v-if="createReq.answerType === 3">
          <el-input v-model="createReq.answer" placeholder="链接地址" />
        </el-form-item>
        <el-form-item label="回答内容" prop="answer" v-if="createReq.answerType === 4">
          <el-upload ref="uploadImgRef" class="avatar-uploader" :before-upload="beforeVideoUpload"
            :show-file-list="false" :http-request="handleFileChange">
            <div class="upload-video-box">
              <el-button size="small" type="primary">选择视频文件</el-button>
              <p class="el-upload__tip">请上传MP4文件并且大小不超过100M的视频文件</p>
              <video width="220" height="160" controls v-if="createReq.answer">
                <source :src="createReq.answer" type="video/mp4" />
                <source :src="createReq.answer" type="video/ogg" />
                您的浏览器不支持 HTML5 video 标签。
              </video>
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="回答内容" prop="answer" v-if="createReq.answerType === 5">
          <el-input v-model="createReq.answer" placeholder="小程序appid" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="createReq.sort" :min="1" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit(addForm)">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.from-icon-box {
  display: flex;

  .label {
    // width: 32rpx;
    // height: 32rpx;
    // display: block;
    margin-left: 10px;
  }
}

.mode-box {
  background-color: #eee;
  display: flex;
  padding: 4px 0px;
  border-radius: 6px;

  .item {
    background-color: #fff;
    padding: 0 10px;
    margin: 0 5px;
    border-radius: 6px;
    text-align: center;
    cursor: pointer;
    height: 26px;
    line-height: 26px;

    &.active {
      background-color: #409eff;
      color: #fff;
    }
  }
}

.avatar-uploader {
  margin-right: 20px;
}

.image-list-box {
  display: flex;

  .upload-img-box {
    margin-right: 10px;
  }
}

.upload-img-box {
  width: 120px;
  height: 120px;
  border: 1px solid #cdd0d6;
  box-sizing: border-box;
  position: relative;
  border-radius: 6px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;

  .preview-img {
    width: 100%;
    height: 100%;
    display: block;
  }

  .operate-box {
    position: absolute;
    z-index: 9;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.5);
    visibility: hidden;
  }

  &:hover .operate-box {
    visibility: visible;
  }
}
</style>
