<script setup lang="ts">
import { exchangeCodeListsApi, exchangeCodeDeleteApi, exchangeCodeCreateApi } from '@/api';
import type { FormInstance, FormRules } from 'element-plus';
import { useRouter } from 'vue-router';
const router = useRouter();
//分页
const total = ref(0);
const loading = ref(false);
const reqForm = reactive({
  merchantGuid: '',
  useStatus: 1,
  pageSize: 10,
  page: 1,
});
const modeList = reactive([
  {
    id: 1,
    name: '未使用',
  },
  {
    id: 2,
    name: '已使用',
  },
]);
const onChangeMode = (value) => {
  reqForm.useStatus = value;
  getList();
};
let goodsList = ref([]);
// let tenantList: any = ref([]);
//获取产品列表
const getList = async () => {
  loading.value = true;

  let goodsRes = await exchangeCodeListsApi(reqForm);
  loading.value = false;
  total.value = goodsRes.data.total;
  goodsList.value = goodsRes.data.data;
};
//获取商户列表
// const getTenantList = async () => {
//   let res = await getTenantListApi();
//   tenantList.value = res.data;
//   reqForm.merchantGuid = tenantList.value[0].guid;

// };

const handlePageChang = async (page) => {
  reqForm.page = page;
  getList();
};

const rules = reactive<FormRules>({
  // merchantGuid: [{ required: true, message: '请选择商家', trigger: 'change' }],
  exchangeCode: [{ required: true, message: '请输入邀请码', trigger: 'change' }],
});
interface AddReq {
  merchantGuid: string;
  exchangeCode: string;
}
const addForm = ref<FormInstance>();
let addReq: AddReq = reactive({
  merchantGuid: '',
  exchangeCode: '',
});
const dialogConfig = reactive({
  dialogtype: 'ADD',
  isShow: false,
});
const onAdd = () => {
  dialogConfig.isShow = true;
  dialogConfig.dialogtype = 'ADD';
};

const onDelete = async (item) => {
  let req = {
    guid: item.guid,
  };
  await exchangeCodeDeleteApi(req);
  ElMessage.success('删除成功');
  getList();
};
const onSubmit = async (formEl) => {
  formEl.validate(async (valid) => {
    if (!valid) {
      return;
    }
    try {
      await exchangeCodeCreateApi(addReq);
      ElMessage.success('新增成功');

      nextTick(() => {
        formEl.resetFields();
        dialogConfig.isShow = false;
      });
      getList();
    } catch (error: any) {
      ElMessage.error(error);
      throw new Error(error);
    }
  });
};
//搜索
const onSearch = () => {
  getList();
};
const generateUniqueCode = (length: number = 6) => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * chars.length);
    result += chars[randomIndex];
  }
  // 获取当前时间戳并转换为字符串后取部分字符（如最后6位）
  const timestampPart = Date.now().toString().slice(-6);
  addReq.exchangeCode = `${result}${timestampPart}`;
  // return `${result}${timestampPart}`;
};
getList();
// getTenantList();
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main v-loading="loading">
        <div class="hearder-box">
          <el-form :inline="true" :model="reqForm" class="demo-form-inline">
            <!-- <el-form-item label="所属商家">
              <el-select v-model="reqForm.merchantGuid" placeholder="请选择">
                <el-option :label="item.merchantName" :value="item.guid" v-for="item in tenantList" :key="item.guid" />
              </el-select>
            </el-form-item> -->
            <el-form-item label="使用状态">
              <div class="mode-box">
                <div :class="['item', { active: reqForm.useStatus === item.id }]" v-for="(item, index) in modeList"
                  :key="index" @click="onChangeMode(item.id)">
                  {{ item.name }}
                </div>
              </div>
            </el-form-item>
            <!-- <el-form-item>
              <el-button type="primary" @click="onSearch">搜索</el-button>
            </el-form-item> -->
            <el-form-item>
              <el-button type="primary" @click="onAdd">新增</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table :data="goodsList" border style="width: 100%">
          <el-table-column prop="sysId" label="sysId" width="80" />
          <el-table-column prop="merchantName" label="所属商家" width="100" />
          <el-table-column prop="exchangeCode" label="邀请码" width="200" />

          <el-table-column prop="createTime" label="创建时间" width="200"> </el-table-column>
          <el-table-column prop="showStatus" label="使用状态" width="160">
            <template #default="scope">
              {{ scope.row.exchangeStatus === 1 ? '未使用' : '已使用' }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作">
            <template #default="scope">
              <!-- <el-button size="small" type="primary" @click="onEdit(scope.row)">删除</el-button> -->
              <el-popconfirm confirm-button-text="确认" cancel-button-text="取消" icon-color="red" title="删除邀请码"
                @confirm="onDelete(scope.row)">
                <template #reference>
                  <el-button size="small" :type="scope.row.status == 2 ? 'success' : 'danger'">删除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </el-main>
      <el-footer>
        <el-pagination background layout="prev,pager, next" :total="total" :current-page="reqForm.page"
          @current-change="handlePageChang" />
      </el-footer>
    </el-container>
    <el-dialog v-model="dialogConfig.isShow" title="创建邀请码" width="500px">
      <el-form ref="addForm" :model="addReq" class="demo-form-inline" label-width="120px" :rules="rules">
        <!-- <el-form-item label="所属商家" prop="merchantGuid">
          <el-select v-model="addReq.merchantGuid" placeholder="请选择">
            <el-option :label="item.merchantName" :value="item.guid" v-for="item in tenantList" :key="item.guid" />
          </el-select>
        </el-form-item> -->
        <el-form-item label="邀请码" prop="exchangeCode">
          <div class="pop-code-flex">
            <el-input v-model="addReq.exchangeCode" placeholder="邀请码内容" />
            <el-button type="primary" class="btn" @click="generateUniqueCode()">随机生成</el-button>
          </div>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit(addForm)">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.pop-code-flex {
  display: flex;

  .btn {
    margin-left: 10px;
  }
}

.mode-box {
  background-color: #eee;
  display: flex;
  padding: 4px 0px;
  border-radius: 6px;
  width: 200px;

  .item {
    background-color: #fff;
    //padding: 0 5px;
    margin: 0 5px;
    flex: 1;
    border-radius: 6px;
    text-align: center;
    cursor: pointer;
    height: 26px;
    line-height: 26px;

    &.active {
      background-color: #409eff;
      color: #fff;
    }
  }
}
</style>
