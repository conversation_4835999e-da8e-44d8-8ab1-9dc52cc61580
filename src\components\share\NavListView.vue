<script setup lang="ts">
import { getIndexUrlsApi, createIndexUrlsApi, updateIndexUrlsApi, deleteIndexUrlsApi } from '@/api';
import type { FormInstance, FormRules } from 'element-plus';
const loading = ref(false);
const urlsReq = reactive({
  merchantGuid: '',
  pageSize: 10,
  page: 1,
});
const urls = ref([]);
const total = ref(0);
const getIndexUrls = async () => {
  let res = await getIndexUrlsApi(urlsReq);
  urls.value = res.data.data;
  total.value = res.data.total;
};
const handlePageChang = async (page) => {
  urlsReq.page = page;
  getIndexUrls();
};
getIndexUrls();
const dialogConfig = reactive({
  dialogtype: 'ADD',
  isShow: false,
});
const urlTypes = [
  { label: 'H5', value: 'H5' },
  { label: '小程序', value: 'MINI' },
  { label: '外部小程序', value: 'OUT_MINI' },
];
const colorTypes = [
  { label: '深色白字', value: 'w1', color: '#fff', bg: 'w' },
  { label: '深色荧字', value: 'w2', color: '#acff20', bg: 'w' },
  { label: '深色紫字', value: 'w3', color: '#1a61de', bg: 'w' },
  {
    label: '深色彩字',
    value: 'wcs',
    color: 'linear-gradient(79deg, #002bff 0%, #ff02db 0%, #0065ff 41%, #00ff71 100%)',
    bg: 'w',
  },
  { label: '浅色白字', value: 'd1', color: '#fff', bg: 'd' },
  { label: '浅色粉字', value: 'd2', color: '#ff00db', bg: 'd' },
  { label: '浅色橙字', value: 'd3', color: '#ff0000', bg: 'd' },
  {
    label: '浅色彩字',
    value: 'dcs',
    color: 'linear-gradient(265deg, rgb(213 3 255) 0%, rgb(255 80 0) 0%, rgb(255 172 0) 41%, rgb(10 255 0) 100%)',
    bg: 'd',
  },
];
let urlList = ref([
  {
    path: 'pages/nav/nav',
    title: 'AI智能体',
  },
  {
    path: 'pages/index/index',
    title: '小艺AI',
  },
  {
    path: 'pages/digit/digit',
    title: '数字人中心',
  },
  {
    path: 'pages/digit/card_qrcode',
    title: '数字客服二维码',
  },
  {
    path: 'pages/my/my',
    title: '我的',
  },
  {
    path: 'pages/exchange/exchange',
    title: '兑换中心',
  },
  {
    path: 'pages/digit/photo',
    title: '专属头像',
  },
  {
    path: 'pages/my/user-info',
    title: '个人中心',
  },
  {
    path: 'pages/my/user-qrcode',
    title: '商务合作',
  },
  {
    path: 'pages/login/login',
    title: '登录',
  },
  {
    path: 'pages/set-tip/set-tip',
    title: '数字人提示语',
  },
  {
    path: 'pages/my/question',
    title: '常见问题',
  },
  {
    path: 'pages/tool-house/tool',
    title: '智能体',
  },
  {
    path: 'pages/tool-house/tool_msg',
    title: '智能体聊天页',
  },

  {
    path: 'page_draw/draw_index/index',
    title: 'AI绘画',
  },
  {
    path: 'page_draw/success/draw_succes',
    title: '绘画详情',
  },
  {
    path: 'page_draw/poster_index/index',
    title: '笨猫相机',
  },
  {
    path: 'page_draw/poster_success/index',
    title: '笨猫详情',
  },
  {
    path: 'page_digit/digit_index/index',
    title: 'AI数字人',
  },
  {
    path: 'page_digit/success/digit_success',
    title: '数字详情',
  },
  {
    path: 'page_channel/channel_index/index',
    title: 'AI市场',
  },
  {
    path: 'page_channel/tool_msg/tool_msg',
    title: '市场聊天页',
  },

  {
    path: 'page_user/user_index/index',
    title: '我的作品',
  },
  {
    path: 'page_card/card_data/card_data',
    title: '数字客服数据',
  },
  {
    path: 'page_card/user_list/user_list',
    title: '访问用户列表',
  },
  {
    path: 'page_card/card_center/card_center',
    title: '我的数字客服',
  },
  {
    path: 'page_card/card_list/card_list',
    title: '我的客服列表',
  },
  {
    path: 'page_card/card_video_list/card_video_list',
    title: '我的客服视频',
  },
  {
    path: 'page_card/card_knowledge_lib/card_knowledge_lib',
    title: '客服知识库',
  },
  {
    path: 'page_card/card_knowledge_detail/card_knowledge_detail',
    title: '知识库管理',
  },
  {
    path: 'page_card/card_create/card_create',
    title: '名片设置',
  },
  {
    path: 'page_card/card_question/card_question',
    title: '基础提问库',
  },
  {
    path: 'page_card/card_prompt/card_prompt',
    title: '一对一咨询',
  },
  {
    path: 'page_card/card_msg/card_msg',
    title: '客服聊天',
  },
]);
type ColorItemType = {
  label: string;
  value: string;
  color: string;
  bg: string;
};
interface CreateReqType {
  merchantGuid: string;
  guid: string;
  urlType: string;
  urlLink: string;
  urlTitle: string;
  colorType: string;
  coloritem: ColorItemType;
}

let createReq = reactive<CreateReqType>({
  merchantGuid: '',
  guid: '',
  urlType: 'H5',
  urlLink: '',
  urlTitle: '',
  colorType: '',
  coloritem: {
    label: '默认',
    value: 'd1',
    color: '#fff',
    bg: 'd',
  },
});
const addForm = ref<FormInstance>();
const rules = reactive<FormRules>({
  merchantGuid: [{ required: true, message: '请选择所属商家', trigger: 'change' }],
  urlType: [{ required: true, message: '请选择跳转类型', trigger: 'change' }],
  urlLink: [{ required: true, message: '请输入跳转地址', trigger: 'change' }],
  urlTitle: [{ required: true, message: '请输入跳转标题', trigger: 'blur' }],
  coloritem: [{ required: true, message: '请选择前端配色方案', trigger: 'change' }],
});
const onAddScene = () => {
  dialogConfig.isShow = true;
  dialogConfig.dialogtype = 'ADD';
};
const onEdit = (item) => {
  createReq.merchantGuid = item.merchantGuid;
  createReq.guid = item.guid;
  createReq.urlType = item.urlType;
  createReq.urlLink = item.urlLink;
  createReq.urlTitle = item.urlTitle;
  colorTypes.forEach((color) => {
    if (item.colorType === color.value) {
      createReq.coloritem = color;
    }
  });
  dialogConfig.isShow = true;
  dialogConfig.dialogtype = 'EDIT';
};
const handleDelete = async (item) => {
  try {
    let guid = item.guid;
    await deleteIndexUrlsApi({ guid });
    getIndexUrls();
    ElMessage.success('删除成功');
  } catch (error: any) {
    ElMessage.success(error);
  }
};
const onSubmit = async (formEl) => {
  formEl.validate(async (valid) => {
    if (!valid) {
      return;
    }
    try {
      createReq.colorType = createReq.coloritem.value;
      if (dialogConfig.dialogtype === 'ADD') {
        await createIndexUrlsApi(createReq);
        ElMessage.success('新增成功');
      } else if (dialogConfig.dialogtype === 'EDIT') {
        await updateIndexUrlsApi(createReq);
        ElMessage.success('修改成功');
      }
      nextTick(() => {
        formEl.resetFields();
        dialogConfig.isShow = false;
      });
      getIndexUrls();
    } catch (error: any) {
      ElMessage.error(error);
      throw new Error(error);
    }
  });
};
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main>
        <div class="header-box">
          <el-form :inline="true" class="demo-form-inline">
            <el-form-item><el-button type="primary" @click="onAddScene">新增配置</el-button></el-form-item>
          </el-form>
        </div>
        <el-table v-loading="loading" :data="urls" border style="width: 100%">
          <el-table-column prop="sysId" label="id" width="80" />
          <el-table-column prop="urlTitle" label="跳转标题" />
          <el-table-column prop="urlType" label="跳转类型" />
          <el-table-column prop="urlLink" label="跳转地址" />
          <el-table-column label="操作">
            <template #default="scope">
              <el-button size="small" type="primary" @click="onEdit(scope.row)">编辑</el-button>
              <el-popconfirm confirm-button-text="确认" cancel-button-text="取消" icon-color="red" title="是否删除该配置?"
                @confirm="handleDelete(scope.row)">
                <template #reference>
                  <el-button size="small" type="danger">删除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </el-main>
      <el-footer>
        <el-pagination background layout="prev,pager, next" :total="total" :current-page="urlsReq.page"
          @current-change="handlePageChang" />
      </el-footer>
    </el-container>
    <el-dialog v-model="dialogConfig.isShow" title="创建/修改 链接配置" width="600" draggable>
      <el-form ref="addForm" :model="createReq" class="demo-form-inline" :rules="rules" label-width="100px">
        <el-form-item label="跳转类型" prop="urlType">
          <el-select v-model="createReq.urlType" placeholder="请选择跳转类型">
            <el-option :label="item.label" :value="item.value" v-for="item in urlTypes" :key="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="分享路径" prop="urlLink">
          <el-input v-model="createReq.urlLink" placeholder="pages/nav/nav"
            v-show="createReq.urlType === 'MINI'"><template #append>
              <el-select v-model="createReq.urlLink" placeholder="选择url" style="width: 115px">
                <el-option v-for="item in urlList" :key="item.path" :label="item.title" :value="item.path" />
              </el-select> </template></el-input>
          <el-input v-model="createReq.urlLink" placeholder="请输入url地址" v-show="createReq.urlType === 'H5'"></el-input>
          <el-input v-model="createReq.urlLink" placeholder="请输入小程序APPID"
            v-show="createReq.urlType === 'OUT_MINI'"></el-input>
        </el-form-item>
        <el-form-item label="分享标题" prop="urlTitle">
          <el-input v-model="createReq.urlTitle" placeholder="请输入跳转标题" />
        </el-form-item>
        <el-form-item label="配色方案" prop="coloritem">
          <el-select v-model="createReq.coloritem" placeholder="请选择前端配色方案">
            <el-option :label="item.label" :value="item" v-for="item in colorTypes" :key="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="样式展示">
          <div class="text-color-box" v-show="createReq.coloritem">
            <div class="white">
              <div :class="[
                'item',
                { 'bg-white': createReq.coloritem.bg == 'w' },
                { 'bg-dark': createReq.coloritem.bg == 'd' },
              ]">
                <text class="color-text" :style="{ backgroundImage: createReq.coloritem.color }"
                  v-if="createReq.coloritem.value == 'dcs' || createReq.coloritem.value == 'wcs'">测试文字1</text>
                <text :style="{ color: createReq.coloritem.color }" v-else>测试文字1</text>
              </div>
            </div>
            <div class="dark">
              <div :class="[
                'item',
                { 'bg-white': createReq.coloritem.bg == 'w' },
                { 'bg-dark': createReq.coloritem.bg == 'd' },
              ]">
                <text class="color-text" :style="{ backgroundImage: createReq.coloritem.color }"
                  v-if="createReq.coloritem.value == 'dcs' || createReq.coloritem.value == 'wcs'">测试文字</text>
                <text :style="{ color: createReq.coloritem.color }" v-else>测试文字</text>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit(addForm)">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.text-color-box {
  width: 400px;
  height: 100px;
  display: flex;

  .item {
    padding: 5px 10px;
    border-radius: 5px;

    &.bg-white {
      background-color: rgba(0, 0, 0, 0.3);

      .color-text {
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }

    &.bg-dark {
      background-color: rgba(255, 255, 255, 0.08);

      .color-text {
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }

  .white {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #dfdfdf;
    width: 200px;
    height: 100%;
  }

  .dark {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #4a4189;
    width: 200px;
    height: 100%;
  }
}
</style>
