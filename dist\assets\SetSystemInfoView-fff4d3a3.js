import{d as O,a as v,r as G,z as P,c as i,b as c,w as n,ag as H,e as J,D as W,o as t,G as h,H as k,I as u,F as _,f as q,T as Q,ah as X,ai as Y,i as V,U as Z,h as ee,E as p,J as ae,aj as w,ak as oe,W as te,k as le,O as ne,P as se,m as ce,n as ue,al as re,am as ie,p as pe,l as de,q as _e,y as fe,_ as me}from"./index-6e8b0ade.js";/* empty css                     *//* empty css                       *//* empty css                 *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                    */import{_ as ge,a as ye}from"./plus-19e9063c.js";/* empty css                       *//* empty css                   */const ve={class:"upload-img-box"},he=["src"],ke={key:1,class:"upload-btn"},Ve={class:"operate-box"},we=O({__name:"SetSystemInfoView",setup(be){const r=W(),m=v({merchantGuid:"",configGroup:""}),b=G(),I=o=>{let s=["image/jpg","image/png","image/jpeg"];if(s.includes(o.type)){if(o.size/1024/1024>2)return p.error("图片必须小于2M"),!1}else return p.warning("当前图片仅支持格式为："+s.join(" ，")),!1;return!0},T=async(o,s)=>{const f=new FormData;f.append("img",o.file);let y=await ae(f);s.value=y.data},F=(o,s)=>{o.value="",b.value[s].clearFiles()},l=v({merchantGuid:"",configKey:"",configValue:"",configGroup:""}),E=G([]),d=async()=>{m.merchantGuid=r.query.guid,m.configGroup=r.query.config;let o=await H(m);E.value=o.data},U=async o=>{l.merchantGuid=r.query.guid,l.configKey=o.key,l.configValue=o.value,l.configGroup=r.query.config,await w(l),p.success("保存成功"),d()},g=v({merchantGuid:"",configKey:""}),B=async o=>{g.merchantGuid=r.query.guid,g.configKey=o.key,l.merchantGuid=r.query.guid,l.configGroup=r.query.config,l.configKey=o.key;let s=await oe(g);s.data===""?te.confirm("默认值为空是否保存",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(async()=>{l.configValue="",await w(l),p({type:"success",message:"重置成功"}),d()}).catch(()=>{p({type:"info",message:"取消重置"})}):(l.configValue=s.data,await w(l),p.success("保存成功"),d())};return P(()=>{d()}),d(),(o,s)=>{const f=le,y=ge,S=ne,R=ye,D=se,z=ce,A=ue,K=re,M=ie,x=pe,j=de,N=_e,$=fe,L=J;return t(),i("div",null,[c(L,{class:"wrapper"},{default:n(()=>[c($,null,{default:n(()=>[c(N,{ref:"addForm",class:"form-box","label-width":"360px"},{default:n(()=>[(t(!0),i(h,null,k(ee(E),(e,C)=>(t(),u(j,{label:e.lable,key:C},{default:n(()=>[e.valueType==="text"?(t(),u(f,{key:0,class:"input",modelValue:e.value,"onUpdate:modelValue":a=>e.value=a,placeholder:e.lable},null,8,["modelValue","onUpdate:modelValue","placeholder"])):_("",!0),e.valueType==="img"?(t(),u(D,{key:1,ref_for:!0,ref_key:"uploadImgRef",ref:b,class:"avatar-uploader","before-upload":I,"show-file-list":!1,"http-request":a=>T(a,e)},{default:n(()=>[q("div",ve,[e.value?(t(),i("img",{key:0,src:e.value,class:"preview-img"},null,8,he)):(t(),i("div",ke,[c(S,{size:"30",color:"#cdd0d6"},{default:n(()=>[c(y)]),_:1})])),Q(q("div",Ve,[c(S,{size:"22",color:"#ffffff",onClick:Y(a=>F(e,C),["stop"])},{default:n(()=>[c(R)]),_:2},1032,["onClick"])],512),[[X,e.value]])])]),_:2},1032,["http-request"])):_("",!0),e.valueType==="select"?(t(),u(A,{key:2,modelValue:e.value,"onUpdate:modelValue":a=>e.value=a,class:"input",placeholder:"Select",multiple:!0,size:"large"},{default:n(()=>[(t(!0),i(h,null,k(e.options,a=>(t(),u(z,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])):_("",!0),e.valueType==="radio"?(t(),u(M,{key:3,modelValue:e.value,"onUpdate:modelValue":a=>e.value=a,class:"input"},{default:n(()=>[(t(!0),i(h,null,k(e.options,a=>(t(),u(K,{label:a.id+"",size:"large",key:a.id},{default:n(()=>[V(Z(a.name),1)]),_:2},1032,["label"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])):_("",!0),c(x,{type:"primary",onClick:a=>U(e)},{default:n(()=>[V("保存")]),_:2},1032,["onClick"]),e.valueType==="img"||e.valueType==="text"?(t(),u(x,{key:4,type:"primary",onClick:a=>B(e)},{default:n(()=>[V("恢复默认")]),_:2},1032,["onClick"])):_("",!0)]),_:2},1032,["label"]))),128))]),_:1},512)]),_:1})]),_:1})])}}});const De=me(we,[["__scopeId","data-v-fdab5880"]]);export{De as default};
