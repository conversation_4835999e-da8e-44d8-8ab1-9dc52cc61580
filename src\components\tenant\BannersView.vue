<script setup lang="ts">
import { getBannerList<PERSON>pi, deleteBanner<PERSON>pi, uploadImg, createBanner<PERSON><PERSON>, updateBannerApi } from '@/api';
import type { FormInstance, FormRules, UploadInstance, UploadProps } from 'element-plus';
import { useRoute } from 'vue-router';

const route = useRoute();
let bannerList: any = ref([]);
const reqForm = reactive({
  merchantGuid: '',
});

//弹窗相关
let dialogVisible = ref(false);
let dialogtype = ref('');
const bannerForm = reactive({
  merchantGuid: '',
  bannerType: '',
  bannerImg: '',
  linkType: 0,
  linkUrl: '',
  bannerDesc: '',
  guid: '',
});
let bannerTypes: any = ref([
  {
    label: 'logo图',
    value: 'logo',
  },
  {
    label: 'slogo图',
    value: 'slogo',
  },
  {
    label: 'my_ad图',
    value: 'my_ad',
  },
]);
let linkType: any = ref([
  {
    label: '不跳转',
    value: 0,
  },
  {
    label: '跳转h5网页',
    value: 1,
  },
  {
    label: '跳转小程序内部页',
    value: 2,
  },
  {
    label: '跳转其他小程序',
    value: 3,
  },
]);
const addForm = ref<FormInstance>();
const rules = reactive<FormRules>({
  bannerType: [{ required: true, message: '请选择banner类型', trigger: 'blur' }],
  bannerImg: [{ required: true, message: '请上传图片', trigger: 'blur' }],
  linkType: [{ required: true, message: '请选择跳转类型', trigger: 'blur' }],
});
const uploadImgRef = ref<UploadInstance>();
// const handleAvatarSuccess: UploadProps['onSuccess'] = (response, uploadFile) => {
//   bannerForm.bannerImg = URL.createObjectURL(uploadFile.raw!);
// };
const beforeAvatarUpload: UploadProps['beforeUpload'] = (rawFile) => {
  let fileTypes = ['image/jpg', 'image/png', 'image/jpeg'];
  if (!fileTypes.includes(rawFile.type)) {
    ElMessage.warning('当前图片仅支持格式为：' + fileTypes.join(' ，'));
    return false;
  } else if (rawFile.size / 1024 / 1024 > 2) {
    ElMessage.error('图片必须小于2M');
    return false;
  }
  return true;
};
const upload = async (file) => {
  const formData = new FormData();
  // 添加要上传的文件名和文件对象,key就是你上传文件的字段名
  formData.append('img', file.file);
  let res = await uploadImg(formData);
  bannerForm.bannerImg = res.data;
};
// const uploadFile: UploadProps['onChange'] = async (uploadFile) => {
//   console.log(uploadFile, 'filefilefile');
//   let typeArray = ['image/jpg', 'image/png', 'image/jpeg'];
//   let raw = uploadFile.raw as File;
//   let isType = typeArray.some((ele) => {
//     return raw.type.indexOf(ele) > -1;
//   });
//   if (!isType) {
//     ElMessage.warning(`文件格式支持：jpg jpeg png `);
//     // deleteImg(file); //不符合条件就删除处理
//     return false;
//   } else if (raw.size / 1024 / 1024 > 2) {
//     ElMessage.error('图片必须小于2M');
//     return false;
//   }
// return true;
// };
const getBannerList = async () => {
  reqForm.merchantGuid = route.query.guid as string;
  let res = await getBannerListApi(reqForm);
  bannerList.value = res.data;
};

//移除图片
const handleRemove = (item) => {
  bannerForm.bannerImg = '';
  uploadImgRef.value!.clearFiles();
};
const onDelete = async (item) => {
  await deleteBannerApi({ guid: item.guid });
  ElMessage.success('删除成功');
  getBannerList();
};
const onSubmit = async (formEl) => {
  bannerForm.merchantGuid = route.query.guid as string;
  formEl.validate(async (valid) => {
    if (!valid) {
      return;
    }
    try {
      if (dialogtype.value === 'add') {
        await createBannerApi(bannerForm);
        ElMessage.success('新增成功');
      } else if (dialogtype.value === 'edit') {
        await updateBannerApi(bannerForm);
        ElMessage.success('修改成功');
      }
      formEl.resetFields();
      dialogVisible.value = false;
      getBannerList();
    } catch (error: any) {
      ElMessage.error(error);
      throw new Error(error);
    }
  });
};
//修改banner‘
const onEdit = (item) => {
  console.log(item, 'itemitem');
  dialogVisible.value = true;
  bannerForm.guid = item.guid;
  bannerForm.bannerType = item.bannerType;
  bannerForm.bannerImg = item.bannerImg;
  bannerForm.linkType = item.linkType;
  bannerForm.bannerDesc = item.bannerDesc;
  dialogtype.value = 'edit';
};
//新增banner
const onAddBanner = () => {
  dialogVisible.value = true;
  dialogtype.value = 'add';
};
onActivated(() => {
  getBannerList();
});
getBannerList();
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main>
        <div class="header-box">
          <el-button type="primary" @click="onAddBanner">上传图片</el-button>
        </div>
        <el-table :data="bannerList" border style="width: 100%">
          <el-table-column prop="sysId" label="Id" width="80" />
          <el-table-column prop="typeText" label="banner类型" width="180" />
          <el-table-column prop="bannerImg" label="banner图片">
            <template #default="scope">
              <el-image :src="scope.row.bannerImg" style="width: 50px; height: 50px; border-radius: 50%"></el-image>
            </template>
          </el-table-column>
          <el-table-column prop="linkType" label="跳转类型">
            <template #default="scope">
              <span v-if="scope.row.linkType === 0">不跳转</span>
              <span v-if="scope.row.linkType === 1">跳转h5网页</span>
              <span v-if="scope.row.linkType === 2">跳转小程序内部页面</span>
              <span v-if="scope.row.linkType === 3">跳转其他小程序</span>
            </template>
          </el-table-column>
          <el-table-column prop="linkUrl" label="跳转地址" />
          <el-table-column label="操作">
            <template #default="scope">
              <el-button size="small" type="primary" @click="onEdit(scope.row)">编辑</el-button>
              <el-button size="small" type="danger" @click="onDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-main>
    </el-container>
    <el-dialog v-model="dialogVisible" title="修改/新增 banner" width="600px">
      <el-form ref="addForm" :model="bannerForm" class="demo-form-inline" label-width="100px" :rules="rules">
        <el-form-item label="banner类型" prop="bannerType">
          <el-select v-model="bannerForm.bannerType" placeholder="请选择">
            <el-option :label="item.label" :value="item.value" v-for="(item, index) in bannerTypes" :key="index" />
          </el-select>
        </el-form-item>
        <el-form-item label="跳转类型" prop="linkType">
          <el-select v-model="bannerForm.linkType" placeholder="请选择">
            <el-option :label="item.label" :value="item.value" v-for="(item, index) in linkType" :key="index" />
          </el-select>
        </el-form-item>
        <el-form-item label="当前图片" v-if="dialogtype === 'edit'">
          <img v-if="bannerForm.bannerImg" :src="bannerForm.bannerImg" class="edit-img" />
        </el-form-item>
        <el-form-item label="图片" prop="bannerImg">
          <!-- :show-file-list="false" -->
          <!-- :on-success="handleAvatarSuccess" -->
          <el-upload ref="uploadImgRef" class="avatar-uploader" list-type="picture-card"
            :before-upload="beforeAvatarUpload" :http-request="upload" :limit="1">
            <!-- <img v-if="bannerForm.bannerImg" :src="bannerForm.bannerImg" class="avatar" /> -->
            <!-- v-else -->
            <el-icon size="30"><i-ep-Plus /></el-icon>
            <template #file="{ file }">
              <div>
                <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                <span class="el-upload-list__item-actions">
                  <span class="el-upload-list__item-delete" @click="handleRemove(file)">
                    <el-icon><i-ep-Delete /></el-icon>
                  </span>
                </span>
              </div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item label="跳转地址">
          <el-input v-model="bannerForm.linkUrl" placeholder="跳转地址" />
        </el-form-item>
        <el-form-item label="banner描述">
          <el-input v-model="bannerForm.bannerDesc" placeholder="banner描述" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit(addForm)">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.avatar-uploader {

  // width: 100px;
  // height: 100px;
  // border: 1px solid #eee;
  // border-radius: 6px;
  // display: flex;
  // justify-content: center;
  // align-items: center;
  // overflow: hidden;
  .avatar {
    width: 100px;
    height: 100px;
  }
}

.header-box {
  margin-bottom: 18px;
}

.edit-img {
  width: 80px;
  height: 80px;
}
</style>
