<script setup lang="ts">
import { useAdminCommonStore } from '@/stores/adminCommon';
import { login } from '@/api';
import { useRouter } from 'vue-router';
import type { FormInstance, FormRules } from 'element-plus';
const store = useAdminCommonStore();
const router = useRouter();
const loginForm = ref<FormInstance>();
const logiLoading = ref(false);
const loginData = reactive({
  userName: '',
  password: '',
  adminType: 'merchant',
});
const rules = reactive<FormRules>({
  userName: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
});
const submitForm = async (formEl) => {
  formEl.validate(async (valid) => {
    if (!valid) {
      return;
    }
    logiLoading.value = true;
    try {
      let data = await login(loginData);
      ElMessage.success('登录成功');
      store.saveLoginInfo(data.data);
      store.saveAdminInfo(data.data);
      router.replace({ name: 'home' });
      logiLoading.value = false;
    } catch (error: any) {
      logiLoading.value = false;
      throw new Error(error);
    }
  });
};
</script>

<template>
  <el-row class="min-h-screen">
    <el-col :lg="16" :md="12" class="left-box">
      <div class="sub-text-box">
        <p class="text-name">一城一智</p>
        <p>商户后台</p>
      </div>
    </el-col>
    <el-col :lg="8" :md="12" class="right-box">
      <p class="login-title">欢迎回来</p>
      <el-form ref="loginForm" :model="loginData" class="login-form" :rules="rules">
        <el-form-item prop="userName">
          <el-input v-model="loginData.userName" autocomplete="off" placeholder="请输入用户名">
            <template #prefix>
              <el-icon>
                <i-ep-User />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input type="password" autocomplete="off" v-model="loginData.password" placeholder="请输入密码">
            <template #prefix>
              <el-icon>
                <i-ep-Lock />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item>
          <el-button
            class="login-btn"
            @click="submitForm(loginForm)"
            :loading="logiLoading"
            round
            color="#626aef"
            native-type="submit"
            >登录
          </el-button>
        </el-form-item>
      </el-form>
    </el-col>
  </el-row>
</template>
<style scoped lang="scss">
.login-title {
  font-weight: bold;
  color: #181818;
  font-size: 36px;
}
.login-form {
  width: 250px;
}
.login-btn {
  width: 250px;
}
.min-h-screen {
  min-height: 100vh;
}
.left-box {
  /* @apply bg-indigo-500 flex justify-center items-center text-light-50 flex-col; */
  background: #3b82f6;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fafafa;
  flex-direction: column;
  .sub-text-box {
    padding: 0 30px;
    .text-name {
      font-weight: bold;
      margin-bottom: 12px;
      font-size: 36px;
    }
  }
}

.right-box {
  /* @apply flex justify-center items-center flex-col; */
  color: #fafafa;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.text-line {
  /* @apply flex items-center justify-center my-5 text-gray-300 space-x-2; */
  color: #d1d5db;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
