import{d as T,r as u,a as v,by as M,bu as N,c as P,b as e,w as o,h as l,A as Z,D as $,bP as Q,e as j,C as H,o as A,T as J,I as K,f as C,i as w,bQ as O,E as p,bR as W,bS as X,p as Y,s as ee,af as ae,t as te,v as oe,y as ne,k as le,l as se,q as ie,Z as re,_ as ue}from"./index-6e8b0ade.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                     *//* empty css                 *//* empty css                        *//* empty css               *//* empty css                  *//* empty css                   */const de={class:"header-box"},ce={class:"switch-box"},me=T({__name:"adminView",setup(_e){const s=$();u(0);const f=u(!1),h=v({zhanhuiGuid:s.query.guid});let b=u([]),i=u(!1);const y=u(),d=v({zhanhuiGuid:s.query.guid,userGuid:"",merchantGuid:""}),E=v({userGuid:[{required:!0,message:"请输入邀请码",trigger:"blur"}]}),m=u(!1),c=async()=>{f.value=!0;let a=await Q(h);f.value=!1,b.value=a.data},x=async a=>{a.validate(async t=>{if(t){m.value=!0;try{await O(d),p.success("新增成功"),i.value=!1,m.value=!1,a.resetFields(),c()}catch(r){throw p.error(r),m.value=!1,new Error(r)}}})},G=async a=>{try{let t=a.guid;await W({adminGuid:t}),c(),p.success("删除成功")}catch(t){p.success(t)}},q=()=>{d.merchantGuid=s.query.guid,i.value=!0};M(()=>{h.zhanhuiGuid=s.query.guid,c()});const k=async a=>{await X({adminGuid:a.guid,isSuperAdmin:a.isSuperAdmin}),c()};return N(()=>s.query.guid,a=>{a&&(h.zhanhuiGuid=s.query.guid,c())}),(a,t)=>{const r=Y,_=ee,F=ae,z=te,S=oe,U=ne,I=j,B=le,V=se,D=ie,R=H,L=re;return A(),P("div",null,[e(I,{class:"wrapper"},{default:o(()=>[J((A(),K(U,null,{default:o(()=>[C("div",de,[e(r,{type:"primary",onClick:q},{default:o(()=>[w("新增管理员")]),_:1})]),e(S,{data:l(b),border:"",style:{width:"100%"}},{default:o(()=>[e(_,{prop:"sysId",label:"Id",width:"80"}),e(_,{prop:"user.guid",label:"邀请码"}),e(_,{prop:"user.nickname",label:"账户名称",width:"180"}),e(_,{label:"操作"},{default:o(n=>[e(F,{"confirm-button-text":"确认","cancel-button-text":"取消","icon-color":"red",title:"是否删除该管理?",onConfirm:g=>G(n.row)},{reference:o(()=>[e(r,{size:"small",type:"danger"},{default:o(()=>[w("删除")]),_:1})]),_:2},1032,["onConfirm"]),C("view",ce,[e(z,{modelValue:n.row.isSuperAdmin,"onUpdate:modelValue":g=>n.row.isSuperAdmin=g,"active-text":"超级管理员","active-value":1,"inactive-value":0,"inactive-text":"管理员",onChange:g=>k(n.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])])]),_:1})]),_:1},8,["data"])]),_:1})),[[L,l(f)]])]),_:1}),e(R,{modelValue:l(i),"onUpdate:modelValue":t[2]||(t[2]=n=>Z(i)?i.value=n:i=n),title:"新增/修改 管理员",width:"600px"},{default:o(()=>[e(D,{ref_key:"addForm",ref:y,model:l(d),rules:l(E)},{default:o(()=>[e(V,{label:"邀请码",prop:"userGuid"},{default:o(()=>[e(B,{modelValue:l(d).userGuid,"onUpdate:modelValue":t[0]||(t[0]=n=>l(d).userGuid=n),autocomplete:"off",placeholder:"请输入邀请码"},null,8,["modelValue"])]),_:1}),e(V,null,{default:o(()=>[e(r,{type:"primary",onClick:t[1]||(t[1]=n=>x(l(y))),loading:l(m)},{default:o(()=>[w("提交")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}});const Ce=ue(me,[["__scopeId","data-v-4ae3a4e8"]]);export{Ce as default};
