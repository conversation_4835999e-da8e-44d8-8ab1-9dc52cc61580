import{d as me,r as _,a as P,c as E,b as e,w as a,h as u,A as H,c4 as ge,E as v,e as _e,C as fe,c8 as ye,o as b,f as r,G as ve,H as be,I as D,i as s,T as we,U as m,F as L,c9 as Ve,ca as he,cb as Se,m as ke,n as xe,l as Ae,k as Ce,p as Ee,q as Fe,s as Ue,ae as Ne,v as Te,x as Re,y as ze,al as Ie,am as Ge,a3 as Pe,t as De,Z as Le,bb as Me,bc as $e,_ as Be}from"./index-6e8b0ade.js";/* empty css                   *//* empty css                  *//* empty css                        *//* empty css                 *//* empty css                  *//* empty css                   *//* empty css                       *//* empty css                 *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                     */const x=A=>(Me("data-v-e621f0a9"),A=A(),$e(),A),qe={class:"search-box"},je={class:"pagination-container"},He={key:0},Oe=x(()=>r("strong",null,"名称：",-1)),Ze=x(()=>r("strong",null,"创作者：",-1)),Je=x(()=>r("strong",null,"当前状态：",-1)),Ke={class:"dialog-footer"},Qe={key:0},We=x(()=>r("strong",null,"名称：",-1)),Xe=x(()=>r("strong",null,"创作者：",-1)),Ye={class:"dialog-footer"},et=me({__name:"UserAgentList",setup(A){const F=_(0),U=_(!1),d=P({merchantGuid:"",categoryGuid:"",agentType:"",auditStatus:"",status:"",isPaid:"",agentName:"",creatorNickname:"",pageSize:10,page:1});let N=_([]),T=_([]);const w=_(!1),R=_(),V=_(null),p=P({guid:"",auditStatus:2,auditRemark:""}),O={auditStatus:[{required:!0,message:"请选择审核状态",trigger:"change"}],auditRemark:[{validator:(l,o,i)=>{p.auditStatus===3&&!o?i(new Error("审核拒绝时必须填写拒绝原因")):i()},trigger:"blur"}]},h=_(!1),z=_(),C=_(null),c=P({guid:"",price:0,isFeatured:0}),y=async()=>{U.value=!0;try{let l=await ye(d);l.data&&Array.isArray(l.data)?(N.value=l.data,F.value=l.data.length):l.data&&l.data.data&&(N.value=l.data.data,F.value=l.data.total)}catch{v.error("获取智能体列表失败")}finally{U.value=!1}},Z=async()=>{try{let l=await ge({merchantGuid:d.merchantGuid});l.data&&Array.isArray(l.data)?T.value=l.data:l.data&&l.data.data&&(T.value=l.data.data),y()}catch{v.error("获取分类列表失败")}},J=()=>{d.page=1,y()},K=()=>{d.categoryGuid="",d.agentType="",d.auditStatus="",d.status="",d.isPaid="",d.agentName="",d.creatorNickname="",d.page=1,y()},Q=l=>{d.page=l,y()},W=l=>{d.pageSize=l,d.page=1,y()},X=l=>{V.value=l,p.guid=l.guid||"",p.auditStatus=l.auditStatus,p.auditRemark="",w.value=!0},Y=async()=>{R.value&&await R.value.validate(async l=>{if(l)try{await Ve({guid:p.guid,auditStatus:p.auditStatus,auditRemark:p.auditRemark}),v.success("审核操作成功"),w.value=!1,y()}catch{v.error("操作失败")}})},ee=l=>{C.value=l,c.guid=l.guid||"",c.price=Number(l.price||0),c.isFeatured=l.isFeatured||0,h.value=!0},te=async()=>{z.value&&await z.value.validate(async l=>{if(l)try{await he({guid:c.guid,price:Number(c.price),isFeatured:c.isFeatured}),v.success("编辑成功"),h.value=!1,y()}catch{v.error("编辑失败")}})},ae=async l=>{try{const o=l.status===1?2:1;await Se({guid:l.guid,status:o}),v.success(`${o===1?"启用":"禁用"}成功`),y()}catch{v.error("状态修改失败")}},le=l=>({1:"内部",2:"dify",3:"coze",4:"阿里云百炼"})[l]||"未知",oe=l=>({1:"",2:"success",3:"warning",4:"info"})[l]||"",M=l=>({1:"待审核",2:"审核通过",3:"审核拒绝"})[l]||"未知",$=l=>({1:"warning",2:"success",3:"danger"})[l]||"";return Z(),(l,o)=>{const i=ke,k=xe,n=Ae,I=Ce,f=Ee,G=Fe,g=Ue,S=Ne,ue=Te,de=Re,se=ze,ie=_e,B=Ie,ne=Ge,q=fe,re=Pe,pe=De,ce=Le;return b(),E("div",null,[e(ie,{class:"wrapper"},{default:a(()=>[e(se,null,{default:a(()=>[r("div",qe,[e(G,{inline:!0,class:"search-form"},{default:a(()=>[e(n,{label:"分类"},{default:a(()=>[e(k,{modelValue:u(d).categoryGuid,"onUpdate:modelValue":o[0]||(o[0]=t=>u(d).categoryGuid=t),placeholder:"请选择分类",clearable:"",style:{width:"200px"}},{default:a(()=>[(b(!0),E(ve,null,be(u(T),t=>(b(),D(i,{label:t.categoryName,value:t.guid,key:t.guid},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(n,{label:"智能体类型"},{default:a(()=>[e(k,{modelValue:u(d).agentType,"onUpdate:modelValue":o[1]||(o[1]=t=>u(d).agentType=t),placeholder:"请选择类型",clearable:"",style:{width:"150px"}},{default:a(()=>[e(i,{label:"内部",value:1}),e(i,{label:"dify",value:2}),e(i,{label:"coze",value:3}),e(i,{label:"阿里云百炼",value:4})]),_:1},8,["modelValue"])]),_:1}),e(n,{label:"审核状态"},{default:a(()=>[e(k,{modelValue:u(d).auditStatus,"onUpdate:modelValue":o[2]||(o[2]=t=>u(d).auditStatus=t),placeholder:"请选择审核状态",clearable:"",style:{width:"150px"}},{default:a(()=>[e(i,{label:"待审核",value:1}),e(i,{label:"审核通过",value:2}),e(i,{label:"审核拒绝",value:3})]),_:1},8,["modelValue"])]),_:1}),e(n,{label:"状态"},{default:a(()=>[e(k,{modelValue:u(d).status,"onUpdate:modelValue":o[3]||(o[3]=t=>u(d).status=t),placeholder:"请选择状态",clearable:"",style:{width:"150px"}},{default:a(()=>[e(i,{label:"启用",value:1}),e(i,{label:"禁用",value:2})]),_:1},8,["modelValue"])]),_:1}),e(n,{label:"是否付费"},{default:a(()=>[e(k,{modelValue:u(d).isPaid,"onUpdate:modelValue":o[4]||(o[4]=t=>u(d).isPaid=t),placeholder:"请选择",clearable:"",style:{width:"150px"}},{default:a(()=>[e(i,{label:"免费",value:0}),e(i,{label:"付费",value:1})]),_:1},8,["modelValue"])]),_:1}),e(n,{label:"智能体名称"},{default:a(()=>[e(I,{modelValue:u(d).agentName,"onUpdate:modelValue":o[5]||(o[5]=t=>u(d).agentName=t),placeholder:"请输入智能体名称",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(n,{label:"创作者昵称"},{default:a(()=>[e(I,{modelValue:u(d).creatorNickname,"onUpdate:modelValue":o[6]||(o[6]=t=>u(d).creatorNickname=t),placeholder:"请输入创作者昵称",clearable:"",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(n,null,{default:a(()=>[e(f,{type:"primary",onClick:J},{default:a(()=>[s("搜索")]),_:1}),e(f,{onClick:K},{default:a(()=>[s("重置")]),_:1})]),_:1})]),_:1})]),we((b(),D(ue,{data:u(N),border:"",style:{width:"100%"}},{default:a(()=>[e(g,{prop:"categoryGuid",label:"分类GUID",width:"320"}),e(g,{prop:"agentType",label:"智能体类型",width:"120"},{default:a(t=>[e(S,{type:oe(t.row.agentType)},{default:a(()=>[s(m(le(t.row.agentType)),1)]),_:2},1032,["type"])]),_:1}),e(g,{prop:"auditStatus",label:"审核状态",width:"100"},{default:a(t=>[e(S,{type:$(t.row.auditStatus)},{default:a(()=>[s(m(M(t.row.auditStatus)),1)]),_:2},1032,["type"])]),_:1}),e(g,{prop:"status",label:"状态",width:"80"},{default:a(t=>[e(S,{type:t.row.status===1?"success":"danger"},{default:a(()=>[s(m(t.row.status===1?"启用":"禁用"),1)]),_:2},1032,["type"])]),_:1}),e(g,{prop:"isPaid",label:"是否付费",width:"100"},{default:a(t=>[e(S,{type:t.row.isPaid===1?"warning":"success"},{default:a(()=>[s(m(t.row.isPaid===1?"付费":"免费"),1)]),_:2},1032,["type"])]),_:1}),e(g,{prop:"isFeatured",label:"是否为精品",width:"100"},{default:a(t=>[e(S,{type:t.row.isFeatured===1?"success":"info"},{default:a(()=>[s(m(t.row.isFeatured===1?"是":"否"),1)]),_:2},1032,["type"])]),_:1}),e(g,{prop:"priceText",label:"价格"}),e(g,{prop:"agentName",label:"智能体名称","show-overflow-tooltip":""}),e(g,{prop:"creator.nickname",label:"创作者昵称","show-overflow-tooltip":""}),e(g,{fixed:"right",label:"操作","min-width":"180"},{default:a(t=>[e(f,{size:"small",type:"primary",onClick:j=>ee(t.row)},{default:a(()=>[s(" 编辑 ")]),_:2},1032,["onClick"]),e(f,{size:"small",type:"primary",onClick:j=>X(t.row),disabled:t.row.auditStatus===2},{default:a(()=>[s(" 审核 ")]),_:2},1032,["onClick","disabled"]),e(f,{size:"small",type:t.row.status===1?"danger":"success",onClick:j=>ae(t.row)},{default:a(()=>[s(m(t.row.status===1?"禁用":"启用"),1)]),_:2},1032,["type","onClick"])]),_:1})]),_:1},8,["data"])),[[ce,u(U)]]),r("div",je,[e(de,{"current-page":u(d).page,"onUpdate:currentPage":o[7]||(o[7]=t=>u(d).page=t),"page-size":u(d).pageSize,"onUpdate:pageSize":o[8]||(o[8]=t=>u(d).pageSize=t),"page-sizes":[10,20,50,100],total:u(F),layout:"total, sizes, prev, pager, next, jumper","prev-text":"上一页","next-text":"下一页",onSizeChange:W,onCurrentChange:Q},null,8,["current-page","page-size","total"])])]),_:1})]),_:1}),e(q,{title:"智能体审核",modelValue:u(w),"onUpdate:modelValue":o[12]||(o[12]=t=>H(w)?w.value=t:null),width:"600px"},{footer:a(()=>[r("span",Ke,[e(f,{onClick:o[11]||(o[11]=t=>w.value=!1)},{default:a(()=>[s("取消")]),_:1}),e(f,{type:"primary",onClick:Y},{default:a(()=>[s("确定")]),_:1})])]),default:a(()=>[e(G,{ref_key:"auditFormRef",ref:R,model:u(p),rules:O,"label-width":"100px"},{default:a(()=>[e(n,{label:"智能体信息"},{default:a(()=>[u(V)?(b(),E("div",He,[r("div",null,[Oe,s(m(u(V).agentName),1)]),r("div",null,[Ze,s(m(u(V).creator.nickname),1)]),r("div",null,[Je,e(S,{type:$(u(V).auditStatus)},{default:a(()=>[s(m(M(u(V).auditStatus)),1)]),_:1},8,["type"])])])):L("",!0)]),_:1}),e(n,{label:"审核状态",prop:"auditStatus"},{default:a(()=>[e(ne,{modelValue:u(p).auditStatus,"onUpdate:modelValue":o[9]||(o[9]=t=>u(p).auditStatus=t)},{default:a(()=>[e(B,{label:2},{default:a(()=>[s("审核通过")]),_:1}),e(B,{label:3},{default:a(()=>[s("审核拒绝")]),_:1})]),_:1},8,["modelValue"])]),_:1}),u(p).auditStatus===3?(b(),D(n,{key:0,label:"拒绝原因",prop:"auditRemark"},{default:a(()=>[e(I,{modelValue:u(p).auditRemark,"onUpdate:modelValue":o[10]||(o[10]=t=>u(p).auditRemark=t),type:"textarea",rows:4,placeholder:"请输入拒绝原因",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1})):L("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue"]),e(q,{title:"智能体编辑",modelValue:u(h),"onUpdate:modelValue":o[16]||(o[16]=t=>H(h)?h.value=t:null),width:"600px"},{footer:a(()=>[r("span",Ye,[e(f,{onClick:o[15]||(o[15]=t=>h.value=!1)},{default:a(()=>[s("取消")]),_:1}),e(f,{type:"primary",onClick:te},{default:a(()=>[s("确定")]),_:1})])]),default:a(()=>[e(G,{ref_key:"editFormRef",ref:z,model:u(c),"label-width":"100px"},{default:a(()=>[e(n,{label:"智能体信息"},{default:a(()=>[u(C)?(b(),E("div",Qe,[r("div",null,[We,s(m(u(C).agentName),1)]),r("div",null,[Xe,s(m(u(C).creator.nickname),1)])])):L("",!0)]),_:1}),e(n,{label:"价格"},{default:a(()=>[e(re,{modelValue:u(c).price,"onUpdate:modelValue":o[13]||(o[13]=t=>u(c).price=t),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),e(n,{label:"是否为精品"},{default:a(()=>[e(pe,{modelValue:u(c).isFeatured,"onUpdate:modelValue":o[14]||(o[14]=t=>u(c).isFeatured=t),"active-value":1,"inactive-value":0,"active-text":"是","inactive-text":"否"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}});const _t=Be(et,[["__scopeId","data-v-e621f0a9"]]);export{_t as default};
