<script setup lang="ts">
import { useAdminCommonStore } from '@/stores/adminCommon';
const store = useAdminCommonStore();
const isCollapse = computed(() => {
  return store.isCollapse;
});
</script>

<template>
  <div class="common-layout">
    <el-container>
      <el-header class="el-header">
        <f-header></f-header>
      </el-header>
      <el-container class="el-container-main">
        <el-aside class="aside-menu" :class="{ collapse: isCollapse }"><f-menu></f-menu></el-aside>
        <el-main>
          <FTagList></FTagList>
          <div class="data-box">
            <!-- <el-scrollbar> -->
            <router-view v-slot="{ Component }">
              <Transition name="fade-transform" mode="out-in" appear>
                <keep-alive>
                  <component :is="Component"></component>
                </keep-alive>
              </Transition>
            </router-view>
            <!-- </el-scrollbar> -->
          </div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<style scoped lang="scss">
.el-aside {
  width: 250px;
}

.el-header {
  height: 40px;
}

.el-container-main {
  background-color: #f3f4f6;
}

.aside-menu {
  overflow: inherit;
  transition: width 0.28s;
  -webkit-transition: width 0.28s;

  &.collapse {
    width: 64px;
  }
}

.el-main {
  padding: 0px 20px;
}

.data-box {
  //顶部  - tab栏
  height: calc(100vh - 40px - 60px);
}

// .fade-enter-from,
// .fade-leave-to {
//   opacity: 0;
// }

// .fade-enter-to,
// .fade-leave-from {
//   opacity: 1;
//   transition: all 1s;
// }</style>
