import{d as Q,a5 as X,bd as Y,a as h,r as U,a6 as Z,be as ee,c,b as r,w as n,ag as ae,e as oe,o as l,G as b,H as V,I as u,h as m,F as i,f as S,T as le,ah as te,ai as ne,i as k,U as se,E as d,J as re,aj as w,ak as ce,W as ue,k as ie,O as de,P as pe,m as _e,n as fe,al as me,am as ve,p as ge,l as ye,q as he,y as be,_ as Ve}from"./index-6e8b0ade.js";/* empty css                     *//* empty css                       *//* empty css                 *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                    */import{_ as ke,a as we}from"./plus-19e9063c.js";/* empty css              */import{T as Ce,E as xe}from"./index.esm-3fc17b22.js";/* empty css                       *//* empty css                   */const Ee={key:0,style:{border:"1px solid #ccc"}},Te={key:1,style:{"margin-bottom":"20px",width:"100%"}},Ge={class:"upload-img-box"},Ue=["src"],Se={key:1,class:"upload-btn"},Be={class:"operate-box"},Fe=Q({__name:"vipConfigView",setup(Ie){const B=X(),v=Y(),F={excludeKeys:["group-image","insertVideo","group-video"]},I=a=>{v.value=a},p=h({merchantGuid:"",configGroup:"big_vip"}),s=h({merchantGuid:"",configKey:"",configValue:"",configGroup:"big_vip"}),C=U(),R=a=>{let t=["image/jpg","image/png","image/jpeg"];if(t.includes(a.type)){if(a.size/1024/1024>2)return d.error("图片必须小于2M"),!1}else return d.warning("当前图片仅支持格式为："+t.join(" ，")),!1;return!0},q=async(a,t)=>{const f=new FormData;f.append("img",a.file);let y=await re(f);t.value=y.data},D=(a,t)=>{a.value="",C.value[t].clearFiles()};let K=Z(()=>B.getTenantInfo.guid);const x=U([]),_=async()=>{p.merchantGuid=K.value;let a=await ae(p);a.data.forEach(t=>{t.valueType==="select"&&(t.value=t.value.split(","))}),x.value=a.data},j=async a=>{s.merchantGuid=p.merchantGuid,s.configKey=a.key,a.valueType==="select"?s.configValue=a.value.join():s.configValue=a.value,await w(s),d.success("保存成功"),_()},g=h({merchantGuid:"",configKey:""}),z=async a=>{g.merchantGuid=p.merchantGuid,g.configKey=a.key,s.merchantGuid=p.merchantGuid,s.configKey=a.key;let t=await ce(g);t.data===""?ue.confirm("默认值为空是否保存",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(async()=>{s.configValue="",await w(s),d({type:"success",message:"重置成功"}),_()}).catch(()=>{d({type:"info",message:"取消重置"})}):(s.configValue=t.data,await w(s),d.success("保存成功"),_())};return ee(()=>{const a=v.value;a!=null&&a.destroy()}),_(),(a,t)=>{const f=ie,y=ke,E=de,A=we,M=pe,N=_e,O=fe,$=me,L=ve,T=ge,P=ye,H=he,J=be,W=oe;return l(),c("div",null,[r(W,{class:"wrapper"},{default:n(()=>[r(J,null,{default:n(()=>[r(H,{ref:"addForm",class:"form-box","label-width":"200px"},{default:n(()=>[(l(!0),c(b,null,V(m(x),(e,G)=>(l(),u(P,{label:e.lable,key:G},{default:n(()=>[e.valueType==="rich_text"?(l(),c("div",Ee,[r(m(Ce),{style:{"border-bottom":"1px solid #ccc"},defaultConfig:F,editor:m(v),mode:"default"},null,8,["editor"]),r(m(xe),{style:{height:"300px","overflow-y":"hidden"},modelValue:e.value,"onUpdate:modelValue":o=>e.value=o,mode:"default",onOnCreated:I},null,8,["modelValue","onUpdate:modelValue"])])):i("",!0),e.valueType==="rich_text"?(l(),c("div",Te)):i("",!0),e.valueType==="text"?(l(),u(f,{key:2,class:"input",modelValue:e.value,"onUpdate:modelValue":o=>e.value=o,placeholder:e.lable},null,8,["modelValue","onUpdate:modelValue","placeholder"])):i("",!0),e.valueType==="img"?(l(),u(M,{key:3,ref_for:!0,ref_key:"uploadImgRef",ref:C,class:"avatar-uploader","before-upload":R,"show-file-list":!1,"http-request":o=>q(o,e)},{default:n(()=>[S("div",Ge,[e.value?(l(),c("img",{key:0,src:e.value,class:"preview-img"},null,8,Ue)):(l(),c("div",Se,[r(E,{size:"30",color:"#cdd0d6"},{default:n(()=>[r(y)]),_:1})])),le(S("div",Be,[r(E,{size:"22",color:"#ffffff",onClick:ne(o=>D(e,G),["stop"])},{default:n(()=>[r(A)]),_:2},1032,["onClick"])],512),[[te,e.value]])])]),_:2},1032,["http-request"])):i("",!0),e.valueType==="select"?(l(),u(O,{key:4,modelValue:e.value,"onUpdate:modelValue":o=>e.value=o,class:"input",placeholder:"Select",multiple:!0,size:"large"},{default:n(()=>[(l(!0),c(b,null,V(e.options,o=>(l(),u(N,{key:o.id,label:o.name,value:o.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])):i("",!0),e.valueType==="radio"?(l(),u(L,{key:5,modelValue:e.value,"onUpdate:modelValue":o=>e.value=o,class:"input"},{default:n(()=>[(l(!0),c(b,null,V(e.options,o=>(l(),u($,{label:o.id+"",size:"large",key:o.id},{default:n(()=>[k(se(o.name),1)]),_:2},1032,["label"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])):i("",!0),r(T,{type:"primary",onClick:o=>j(e)},{default:n(()=>[k("保存")]),_:2},1032,["onClick"]),e.valueType==="img"||e.valueType==="text"?(l(),u(T,{key:6,type:"primary",onClick:o=>z(e)},{default:n(()=>[k("恢复默认")]),_:2},1032,["onClick"])):i("",!0)]),_:2},1032,["label"]))),128))]),_:1},512)]),_:1})]),_:1})])}}});const Je=Ve(Fe,[["__scopeId","data-v-b1870260"]]);export{Je as default};
