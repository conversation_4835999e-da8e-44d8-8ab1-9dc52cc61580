import{d as z,r,a as w,c as u,b as e,w as a,aJ as F,e as P,o as c,f,h as s,G as R,H as q,aC as G,U as M,i as O,T as U,I as A,k as H,l as J,p as Y,q as Z,s as $,N as j,v as K,y as Q,x as W,Y as X,Z as ee,_ as te}from"./index-6e8b0ade.js";/* empty css                   *//* empty css                      *//* empty css                 *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                        *//* empty css                     */const ae={class:"header-box"},oe={class:"mode-box"},le=["onClick"],se=z({__name:"DrawListView",setup(ne){r([]);const i=w({merchantGuid:"",nickname:"",status:"",page:1,pageSize:9}),m=r(0),g=r([]);let p=r(!1);const d=async()=>{p.value=!0;let o=await F(i);m.value=o.data.total,o.data.data.forEach(t=>{switch(t.imageResult.length===0?t.path="":t.path=t.imageResult[0],t.orderStatus){case"success":t.status="执行成功";break;case"fail":t.status="执行失败";break;case"wait":t.status="等待执行";break;case"doing":t.status="正在执行";break}}),g.value=o.data.data,p.value=!1},b=async o=>{i.page=o,d()};let h=r("");const v=w([{id:0,type:"wait",name:"等待执行"},{id:1,type:"doing",name:"正在执行"},{id:2,type:"fail",name:"执行失败"},{id:3,type:"success",name:"执行成功"}]),y=o=>{h.value=o,i.status=o},k=()=>{d()};return d(),(o,t)=>{const x=H,_=J,C=Y,E=Z,l=$,L=j,I=K,V=Q,N=W,S=X,B=P,D=ee;return c(),u("div",null,[e(B,{class:"wrapper"},{default:a(()=>[e(V,null,{default:a(()=>[f("div",ae,[e(E,{inline:!0,model:s(i),class:"demo-form-inline"},{default:a(()=>[e(_,{label:"用户昵称"},{default:a(()=>[e(x,{modelValue:s(i).nickname,"onUpdate:modelValue":t[0]||(t[0]=n=>s(i).nickname=n),placeholder:"用户昵称"},null,8,["modelValue"])]),_:1}),e(_,{label:"场景类型"},{default:a(()=>[f("div",oe,[(c(!0),u(R,null,q(s(v),(n,T)=>(c(),u("div",{class:G(["item",{active:s(h)===n.type}]),key:T,onClick:ie=>y(n.type)},M(n.name),11,le))),128))])]),_:1}),e(_,null,{default:a(()=>[e(C,{type:"primary",onClick:k},{default:a(()=>[O("搜索")]),_:1})]),_:1})]),_:1},8,["model"])]),U((c(),A(I,{data:s(g),border:"",style:{width:"100%"}},{default:a(()=>[e(l,{prop:"sysId",label:"id",width:"80"}),e(l,{prop:"orderNo",label:"订单编号",width:"150"}),e(l,{prop:"userPrompt","show-overflow-tooltip":!0,label:"提示词"}),e(l,{prop:"path",label:"图片",width:"100"},{default:a(n=>[e(L,{src:n.row.path,"initial-index":0,"preview-src-list":n.row.imageResult,"z-index":9,"preview-teleported":!0,style:{width:"50px",height:"50px"}},null,8,["src","preview-src-list"])]),_:1}),e(l,{prop:"imageNum",label:"绘画数量",width:"150"}),e(l,{prop:"imageSize",label:"图片尺寸",width:"150"}),e(l,{prop:"payPoint",label:"支付金额",width:"150"}),e(l,{prop:"status",label:"状态",width:"150"}),e(l,{prop:"modifyTime",label:"时间",width:"250"})]),_:1},8,["data"])),[[D,s(p)]])]),_:1}),e(S,null,{default:a(()=>[e(N,{background:"",layout:"prev,pager, next",total:s(m),"current-page":s(i).page,onCurrentChange:b},null,8,["total","current-page"])]),_:1})]),_:1})])}}});const we=te(se,[["__scopeId","data-v-d8987aa4"]]);export{we as default};
