import{d as H,r as f,a as _,c as x,b as e,w as t,h as s,bT as Y,e as Z,C as j,u as J,o as h,T as K,I as O,f as b,G as Q,H as W,aC as X,U as k,i as g,bU as ee,E as w,bV as te,ax as oe,l as ae,p as ne,q as se,s as le,af as re,v as de,y as ie,x as ce,Y as ue,k as pe,Z as me,_ as _e}from"./index-6e8b0ade.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                 *//* empty css                      *//* empty css               *//* empty css                  *//* empty css                        *//* empty css                   *//* empty css                     */const ge={class:"hearder-box"},fe={class:"mode-box"},he=["onClick"],Ce={class:"pop-code-flex"},xe=H({__name:"redeemCodes",setup(be){J();const y=f(0),C=f(!1),d=_({merchantGuid:"",useStatus:1,pageSize:10,page:1}),D=_([{id:1,name:"未使用"},{id:2,name:"已使用"}]),V=a=>{d.useStatus=a,c()};let v=f([]);const c=async()=>{C.value=!0;let a=await Y(d);C.value=!1,y.value=a.data.total,v.value=a.data.data},F=async a=>{d.page=a,c()},I=_({exchangeCode:[{required:!0,message:"请输入邀请码",trigger:"change"}]}),E=f();let u=_({merchantGuid:"",exchangeCode:""});const p=_({dialogtype:"ADD",isShow:!1}),T=()=>{p.isShow=!0,p.dialogtype="ADD"},q=async a=>{let o={guid:a.guid};await ee(o),w.success("删除成功"),c()},A=async a=>{a.validate(async o=>{if(o)try{await te(u),w.success("新增成功"),oe(()=>{a.resetFields(),p.isShow=!1}),c()}catch(l){throw w.error(l),new Error(l)}})},L=(a=6)=>{const o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";let l="";for(let m=0;m<a;m++){const r=Math.floor(Math.random()*o.length);l+=o[r]}const i=Date.now().toString().slice(-6);u.exchangeCode=`${l}${i}`};return c(),(a,o)=>{const l=ae,i=ne,m=se,r=le,B=re,M=de,U=ie,$=ce,N=ue,P=Z,z=pe,G=j,R=me;return h(),x("div",null,[e(P,{class:"wrapper"},{default:t(()=>[K((h(),O(U,null,{default:t(()=>[b("div",ge,[e(m,{inline:!0,model:s(d),class:"demo-form-inline"},{default:t(()=>[e(l,{label:"使用状态"},{default:t(()=>[b("div",fe,[(h(!0),x(Q,null,W(s(D),(n,S)=>(h(),x("div",{class:X(["item",{active:s(d).useStatus===n.id}]),key:S,onClick:we=>V(n.id)},k(n.name),11,he))),128))])]),_:1}),e(l,null,{default:t(()=>[e(i,{type:"primary",onClick:T},{default:t(()=>[g("新增")]),_:1})]),_:1})]),_:1},8,["model"])]),e(M,{data:s(v),border:"",style:{width:"100%"}},{default:t(()=>[e(r,{prop:"sysId",label:"sysId",width:"80"}),e(r,{prop:"merchantName",label:"所属商家",width:"100"}),e(r,{prop:"exchangeCode",label:"邀请码",width:"200"}),e(r,{prop:"createTime",label:"创建时间",width:"200"}),e(r,{prop:"showStatus",label:"使用状态",width:"160"},{default:t(n=>[g(k(n.row.exchangeStatus===1?"未使用":"已使用"),1)]),_:1}),e(r,{fixed:"right",label:"操作"},{default:t(n=>[e(B,{"confirm-button-text":"确认","cancel-button-text":"取消","icon-color":"red",title:"删除邀请码",onConfirm:S=>q(n.row)},{reference:t(()=>[e(i,{size:"small",type:n.row.status==2?"success":"danger"},{default:t(()=>[g("删除")]),_:2},1032,["type"])]),_:2},1032,["onConfirm"])]),_:1})]),_:1},8,["data"])]),_:1})),[[R,s(C)]]),e(N,null,{default:t(()=>[e($,{background:"",layout:"prev,pager, next",total:s(y),"current-page":s(d).page,onCurrentChange:F},null,8,["total","current-page"])]),_:1})]),_:1}),e(G,{modelValue:s(p).isShow,"onUpdate:modelValue":o[3]||(o[3]=n=>s(p).isShow=n),title:"创建邀请码",width:"500px"},{default:t(()=>[e(m,{ref_key:"addForm",ref:E,model:s(u),class:"demo-form-inline","label-width":"120px",rules:s(I)},{default:t(()=>[e(l,{label:"邀请码",prop:"exchangeCode"},{default:t(()=>[b("div",Ce,[e(z,{modelValue:s(u).exchangeCode,"onUpdate:modelValue":o[0]||(o[0]=n=>s(u).exchangeCode=n),placeholder:"邀请码内容"},null,8,["modelValue"]),e(i,{type:"primary",class:"btn",onClick:o[1]||(o[1]=n=>L())},{default:t(()=>[g("随机生成")]),_:1})])]),_:1}),e(l,null,{default:t(()=>[e(i,{type:"primary",onClick:o[2]||(o[2]=n=>A(s(E)))},{default:t(()=>[g("提交")]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}});const Ae=_e(xe,[["__scopeId","data-v-0a7b1369"]]);export{Ae as default};
