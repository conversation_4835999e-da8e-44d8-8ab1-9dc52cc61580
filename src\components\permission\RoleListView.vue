<template>
	<div class="role-container">
		<el-container class="wrapper">
			<el-main>
				<div class="header-box">
					<el-form :inline="true" class="demo-form-inline">
						<el-form-item>
							<el-button type="primary" @click="handleAdd">新增角色</el-button>
						</el-form-item>
					</el-form>
				</div>
				<el-table :data="roleList" border v-loading="loading">
					<el-table-column prop="roleName" label="角色名称" />
					<el-table-column prop="roleIntro" label="角色描述" />
					<el-table-column prop="status" label="状态">
						<template #default="{ row }">
							<el-tag :type="row.status ? 'success' : 'info'">
								{{ row.status ? '启用' : '禁用' }}
							</el-tag>
						</template>
					</el-table-column>
					<!-- <el-table-column prop="isDefaultForZhanhui" label="展会默认角色">
						<template #default="{ row }">
							<el-tag :type="row.isDefaultForZhanhui ? 'warning' : 'info'">
								{{ row.isDefaultForZhanhui ? '是' : '否' }}
							</el-tag>
						</template>
					</el-table-column> -->
					<el-table-column label="操作" width="200">
						<template #default="{ row }">
							<el-button type="primary" link :loading="editLoading === row.sysId" @click="handleEdit(row)">
								编辑
							</el-button>
							<el-button type="danger" link @click="handleDelete(row)">删除</el-button>
						</template>
					</el-table-column>
				</el-table>
			</el-main>
		</el-container>

		<!-- 角色表单对话框 -->
		<el-dialog :title="dialogTitle" v-model="dialogVisible" width="700px">
			<el-form ref="formRef" :model="formData" :rules="rules" label-width="120px">
				<el-form-item label="角色名称" prop="roleName">
					<el-input v-model="formData.roleName" placeholder="请输入角色名称" />
				</el-form-item>
				<el-form-item label="角色描述" prop="roleIntro">
					<el-input v-model="formData.roleIntro" placeholder="请输入角色描述" />
				</el-form-item>
				<el-form-item label="状态" prop="status">
					<el-switch v-model="formData.status" :active-value="1" :inactive-value="0" active-text="启用"
						inactive-text="禁用" />
				</el-form-item>
				<!-- <el-form-item label="展会默认角色" prop="isDefaultForZhanhui">
					<el-switch v-model="formData.isDefaultForZhanhui" :active-value="1" :inactive-value="0" active-text="是"
						inactive-text="否" />
				</el-form-item> -->
				<el-form-item label="权限设置" prop="permissions">
					<div class="permission-tree-container">
						<el-tree ref="permissionTree" :data="permissionList" show-checkbox node-key="sysId"
							:props="{ label: 'name', children: 'children' }" default-expand-all :expand-on-click-node="false"
							:check-on-click-node="true" />
					</div>
				</el-form-item>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="dialogVisible = false">取消</el-button>
					<el-button type="primary" @click="handleSubmit">确定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
const store = useAdminCommonStore();
import { useAdminCommonStore } from '@/stores/adminCommon';
import {
	getRoleListApi,
	createRoleApi,
	editRoleApi,
	deleteRoleApi,
	getRoleDetailApi,
	permissionMenuListApi,
} from '@/api'

interface RoleItem {
	sysId: number | string;
	roleName: string;
	roleIntro: string;
	status: number;
	merchantGuid: string;
	isDefaultForZhanhui: number;
	permissions?: string[];
}

// 角色列表数据
const roleList = ref<any[]>([])
const loading = ref(false)

// 权限树数据
const permissionList = ref<any[]>([])
const permissionTree = ref()

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref<FormInstance>()
const actionType = ref('add')
let merchantGuid = computed(() => {
	return store.getTenantInfo.guid;
});
// 表单数据
const formData = reactive({
	sysId: null,
	roleName: '',
	roleIntro: '',
	status: 1,
	merchantGuid: '',
	isDefaultForZhanhui: 0,
	permissions: []
})

// 表单验证规则
const rules = {
	roleName: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
	roleIntro: [{ required: true, message: '请输入角色描述', trigger: 'blur' }],
	merchantGuid: [{ required: true, message: '请输入商户标识', trigger: 'blur' }],
	// isDefaultForZhanhui: [{ required: true, message: '请选择是否为展会默认角色', trigger: 'change' }]
}
const roleReq = reactive({
	merchantGuid: '',
	pageSize: 100,
	page: 1
});
// 获取角色列表
const getRoleList = async () => {
	loading.value = true
	try {
		const res = await getRoleListApi(roleReq)
		console.log('res', res)
		roleList.value = res.data.list
	} catch (error) {
		ElMessage.error('获取角色列表失败')
	} finally {
		loading.value = false
	}
}

// 获取权限菜单列表
const getPermissionMenuList = async () => {
	try {
		const res = await permissionMenuListApi({})
		permissionList.value = res.data
	} catch (error) {
		ElMessage.error('获取权限菜单失败')
	}
}

// 新增角色
const handleAdd = () => {
	actionType.value = 'add'
	dialogTitle.value = '新增角色'

	// 重置表单数据
	Object.assign(formData, {
		sysId: null,
		roleName: '',
		roleIntro: '',
		status: 1,
		merchantGuid: '',
		isDefaultForZhanhui: 0,
		permissions: []
	})

	// 重置权限树选中状态
	nextTick(() => {
		if (permissionTree.value) {
			permissionTree.value.setCheckedKeys([])
		}
	})

	dialogVisible.value = true
}
const editLoading = ref<number>(0)
// 编辑角色
const handleEdit = async (row) => {
	editLoading.value = row.sysId // 设置正在编辑的角色ID
	actionType.value = 'edit'
	dialogTitle.value = '编辑角色'
	try {
		// 先确保权限树数据已加载
		if (permissionList.value.length === 0) {
			await getPermissionMenuList();
		}
		// 获取角色详情
		const res = await getRoleDetailApi({ sysId: row.sysId })
		const roleDetail = res.data

		// 填充表单数据
		Object.assign(formData, {
			sysId: roleDetail.sysId,
			roleName: roleDetail.roleName,
			roleIntro: roleDetail.roleIntro,
			status: roleDetail.status,
			merchantGuid: roleDetail.merchantGuid,
			isDefaultForZhanhui: roleDetail.isDefaultForZhanhui,
			permissions: roleDetail.permissions || []
		})

		dialogVisible.value = true

		// 使用 nextTick 确保 DOM 更新后再设置选中状态
		await nextTick()
		if (permissionTree.value && formData.permissions) {
			permissionTree.value.setCheckedKeys(formData.permissions)
		}
	} catch (error) {
		ElMessage.error('获取角色详情失败')
	} finally {
		editLoading.value = 0 // 重置加载状态
	}
}

// 删除角色
const handleDelete = (row) => {
	ElMessageBox.confirm('确认删除该角色吗？', '提示', {
		type: 'warning'
	}).then(async () => {
		try {
			await deleteRoleApi({ sysId: row.sysId })
			ElMessage.success('删除成功')
			getRoleList()
		} catch (error) {
			ElMessage.error('删除失败')
		}
	})
}

// 提交表单
const handleSubmit = async () => {
	if (!formRef.value) return
	await formRef.value.validate(async (valid) => {
		if (valid) {
			try {
				// 获取选中的权限ID
				if (permissionTree.value) {
					formData.permissions = permissionTree.value.getCheckedKeys()
				}

				if (actionType.value === 'edit') {
					await editRoleApi(formData)
					ElMessage.success('更新成功')
				} else {
					await createRoleApi(formData)
					ElMessage.success('创建成功')
				}

				dialogVisible.value = false
				getRoleList()
			} catch (error) {
				ElMessage.error(actionType.value === 'edit' ? '更新失败' : '创建失败')
			}
		}
	})
}
// type TenantListItem = {
//     guid: string;
//     merchantName: string;
// };
// const tenantList = ref<TenantListItem[]>([]);
// const getTenantList = async () => {
//     let res = await getTenantListApi();
//     if (res.data && Array.isArray(res.data)) {
//         tenantList.value = res.data;
//         if (res.data.length > 0) {
//             roleReq.merchantGuid = res.data[0].guid;
//             getRoleList();
//         }
//     }
// };
// 初始化
onMounted(() => {
	// getTenantList()
	getRoleList();
	getPermissionMenuList()
})
</script>

<style scoped>
.permission-tree-container {
	/* height: 300px; */
	overflow-y: auto;
	border: 1px solid #dcdfe6;
	border-radius: 4px;
	padding: 10px;
	overflow: hidden;
}

/* 弹窗样式调整 */
:deep(.role-dialog) {
	max-height: 80vh;
}

:deep(.role-dialog .el-dialog__body) {
	max-height: 60vh;
	overflow-y: auto;
	padding: 20px;
}

/* 权限树样式 - 移除内部滚动条 */
.permission-tree-container :deep(.el-tree) {
	overflow: visible !important;
}

.permission-tree-container :deep(.el-scrollbar) {
	height: 100% !important;
}

.permission-tree-container :deep(.el-scrollbar__view) {
	overflow: visible !important;
}
</style>
