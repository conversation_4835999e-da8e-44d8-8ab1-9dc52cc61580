import router from '../router/index';
import { useAdminCommonStore } from '@/stores/adminCommon';

router.beforeEach((to, from, next) => {
  const adminCommonStore = useAdminCommonStore();

  //显示加载进度条
  // start()
  const token = adminCommonStore.getToken;
  //判断是否登录
  if (!token() && to.path != '/login') {
    ElMessage.error('请先登录');
    return next({
      path: '/login',
    });
  }
  //防止重复登录
  if (token() && to.path == '/login') {
    return next({
      path: '/',
    });
  }
  // if (token() && to.path == '/' && from.path != '/login') {
  // }
  //设置页面标题
  window.document.title = to.meta.title ? to.meta.title + ' | 一智一城' : '一智一城';
  next();
});
export default router;
