<script setup lang="ts">
import { getTenantListPageApi, editTenantApi, createTenantApi } from '@/api';
// import type { FormInstance, FormRules } from 'element-plus';
import { useRouter } from 'vue-router';
const router = useRouter();
let tenantList: any = ref([]);

// 分页参数
const total = ref(0);
const loading = ref(false);

// 搜索筛选参数
const searchParams = reactive({
  merchantGuid: '',
  merchantName: '',
  status: '',
  pageSize: 10,
  page: 1,
});
const getTenantList = async () => {
  loading.value = true;
  try {
    let res = await getTenantListPageApi(searchParams);
    if (res.data && res.data.data) {
      // 如果返回的是分页数据格式
      tenantList.value = res.data.data;
      total.value = res.data.total || res.data.data.length;
    } else {
      // 如果返回的是直接数组格式
      tenantList.value = res.data;
      total.value = res.data.length;
    }
  } catch (error) {
    ElMessage.error('获取商户列表失败');
  } finally {
    loading.value = false;
  }
};

// 搜索筛选功能
const handleSearch = () => {
  searchParams.page = 1; // 重置到第一页
  getTenantList();
};

// 重置搜索
const handleReset = () => {
  searchParams.merchantGuid = '';
  searchParams.merchantName = '';
  searchParams.status = '';
  searchParams.page = 1; // 重置到第一页
  getTenantList();
};

// 分页切换
const handlePageChange = (page: number) => {
  searchParams.page = page;
  getTenantList();
};

// 每页条数切换
const handleSizeChange = (size: number) => {
  searchParams.pageSize = size;
  searchParams.page = 1; // 重置到第一页
  getTenantList();
};
//弹窗
let sattusLoading = ref(false);
let reqAddForm = reactive({
  merchantGuid: '',
  merchantName: '',
  merchantDesc: '',
  merchantChatCount: 0,
  status: 200,
});
const onFinish = (item) => {
  router.push({
    name: 'setSystem',
    query: {
      guid: item.guid,
      config: 'show',
    },
  });
};
const onUser = (item) => {
  router.push({
    name: 'setSystem',
    query: {
      guid: item.guid,
      config: 'user',
    },
  });
};
const onChat = (item) => {
  router.push({
    name: 'tenantChats',
    query: {
      guid: item.guid,
    },
  });
};
const onBanner = (item) => {
  router.push({
    name: 'bannerList',
    query: {
      guid: item.guid,
    },
  });
};
const onNav = (item) => {
  router.push({
    name: 'setSystem',
    query: {
      guid: item.guid,
      config: 'tools_show',
    },
  });
};
const onStatusChange = async (item) => {
  reqAddForm = Object.assign(reqAddForm, item);
  reqAddForm.merchantGuid = item.guid;
  try {
    sattusLoading.value = true;
    await editTenantApi(reqAddForm);
    ElMessage.success('修改成功');
    sattusLoading.value = false;
  } catch (error: any) {
    sattusLoading.value = false;
    throw new Error(error);
  }
};
getTenantList();
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main>
        <!-- 搜索筛选区域 -->
        <div class="search-box">
          <el-form :inline="true" class="search-form">
            <el-form-item label="GUID">
              <el-input v-model="searchParams.merchantGuid" placeholder="请输入GUID" clearable style="width: 200px" />
            </el-form-item>
            <el-form-item label="商户名称">
              <el-input v-model="searchParams.merchantName" placeholder="请输入商户名称" clearable style="width: 200px" />
            </el-form-item>
            <el-form-item label="商家状态">
              <el-select v-model="searchParams.status" placeholder="请选择状态" clearable style="width: 150px">
                <el-option label="启用" :value="200" />
                <el-option label="禁用" :value="400" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table :data="tenantList" border style="width: 100%">
          <el-table-column prop="sysId" label="商户Id" width="80" />
          <el-table-column prop="guid" label="guid" width="320" />
          <el-table-column prop="merchantName" label="商户名称" width="180" />
          <el-table-column prop="merchantDesc" :show-overflow-tooltip="true" label="商户备注" width="200" />
          <el-table-column prop="createTime" label="注册时间" width="200" />
          <el-table-column prop="status" label="商家状态" width="160">
            <template #default="scope">
              <el-switch v-model="scope.row.status" active-text="启用" inactive-text="禁用" :active-value="200"
                :inactive-value="400" :loading="sattusLoading" @change="onStatusChange(scope.row)" />
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <!-- <el-button size="small" type="primary" @click="onEdit(scope.row)">商家编辑</el-button> -->
              <!-- <el-button size="small" type="primary" @click="onSystem(scope.row)">系统设置</el-button> -->
              <el-button size="small" type="primary" @click="onFinish(scope.row)">商家装修</el-button>
              <el-button size="small" type="primary" @click="onChat(scope.row)">点数商品</el-button>
              <el-button size="small" type="primary" @click="onUser(scope.row)">用户设置</el-button>
              <el-button size="small" type="primary" @click="onBanner(scope.row)">banner设置</el-button>
              <el-button size="small" type="primary" @click="onNav(scope.row)">导航页设置</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <div class="pagination-container">
          <el-pagination v-model:current-page="searchParams.page" v-model:page-size="searchParams.pageSize"
            :page-sizes="[10, 20, 50, 100]" :total="total" layout="total, sizes, prev, pager, next, jumper"
            :prev-text="'上一页'" :next-text="'下一页'" @size-change="handleSizeChange" @current-change="handlePageChange" />
        </div>
      </el-main>
    </el-container>
    <!-- <el-dialog v-model="dialogVisible" title="新增/编辑 商家" width="600px">
      <el-form ref="addForm" :model="reqAddForm" class="demo-form-inline" label-width="100px" :rules="rules">
        <el-form-item label="商家名称" prop="goodsName">
          <el-input v-model="reqAddForm.merchantName" placeholder="商家名称" />
        </el-form-item>
        <el-form-item label="商家描述" prop="goodsDesc">
          <el-input v-model="reqAddForm.merchantDesc" placeholder="商家描述" />
        </el-form-item>
        <el-form-item label="商家聊天点数" prop="chatCount">
          <el-input-number v-model="reqAddForm.merchantChatCount" :min="0" />
        </el-form-item>
        <el-form-item label="商家状态" prop="price" v-if="dialogtype === 'edit'">
          <el-switch
            v-model="reqAddForm.status"
            active-text="启用"
            inactive-text="禁用"
            :active-value="200"
            :inactive-value="400" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit(addForm)">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog> -->
  </div>
</template>

<style scoped lang="scss">
.search-box {
  background: #f5f7fa;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.search-form {
  margin: 0;
}

.search-form .el-form-item {
  margin-bottom: 0;
}

.header-box {
  margin-bottom: 18px;
}

.pagination-container {
  display: flex;
  justify-content: flex-start;
  margin-top: 20px;
  padding: 20px 0;
}
</style>
