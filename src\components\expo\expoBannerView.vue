<script setup lang="ts">
import '@wangeditor/editor/dist/css/style.css';
import {
  uploadImg,
  uploadVideo,
  bannerCreateApi,
  bannerListsApi,
  bannerUpdateApi,
  bannerDeleteApi,
  bannerTitleUpdateApi,
  expoFaqListsApi,
} from '@/api';
import { useRoute } from 'vue-router';
import type { UploadProps, FormInstance, FormRules } from 'element-plus';
const route = useRoute();
//分页
const loading = ref(false);
const addForm = ref<FormInstance>();
const bannerForm = ref<FormInstance>();

const bannerType = reactive([
  {
    id: 0,
    type: 1,
    name: '议题轮播',
  },
  {
    id: 1,
    type: 2,
    name: '嘉宾轮播',
  },
  {
    id: 2,
    type: 3,
    name: '推荐企业',
  },
]);
const jumpType = reactive([
  {
    id: 0,
    type: 1,
    name: '不跳转',
  },
  {
    id: 1,
    type: 2,
    name: '跳转常见问题',
  },
  {
    id: 1,
    type: 3,
    name: '播放视频',
  },
]);
const total = ref(0);
const reqListForm = reactive({
  zhanhuiGuid: '',
  bannerType: 1,
  pageSize: 10,
  page: 1,
});
const onChangeMode = (value) => {
  reqListForm.bannerType = value;
  getList();
};
let editBannerReq = reactive({
  zhanhuiGuid: '',
  bannerType: 1,
  title: '',
});
let createReq = reactive({
  merchantGuid: '',
  zhanhuiGuid: '',
  bannerType: 1,
  title: '',
  content: '',
  image: '',
  jumpType: 1,
  jumpValue: '',
  sort: 1,
});
const fromType = ref('ADD');
const dialogConfig = ref(false);
const bannerDialog = ref(false);

let goodsList = ref([]);
//获取问题列表
const getList = async () => {
  loading.value = true;
  let goodsRes = await bannerListsApi(reqListForm);
  loading.value = false;
  total.value = goodsRes.data.total;
  goodsList.value = goodsRes.data.data;
};
const handlePageChang = async (page) => {
  reqListForm.page = page;
  getList();
};
const rules = reactive<FormRules>({
  bannerType: [{ required: true, message: '请选择轮播类型', trigger: 'change' }],
  title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
  content: [{ required: true, message: '请选择轮播内容', trigger: 'blur' }],
  image: [{ required: true, message: '请上传轮播图', trigger: 'change' }],
  jumpType: [{ required: true, message: '请选择跳转类型', trigger: 'blur' }],
  jumpValue: [{ required: true, message: '请输入跳转值', trigger: 'blur' }],
});
const onAdd = () => {
  dialogConfig.value = true;
  fromType.value = 'ADD';
};
const onEdit = (item) => {
  dialogConfig.value = true;
  fromType.value = 'EDIT';
  if (addForm.value) {
    addForm.value.resetFields();
  }
  createReq = Object.assign(createReq, item);
};
const handleDelete = async (item) => {
  try {
    let guid = item.guid;
    loading.value = true;
    await bannerDeleteApi({ guid });
    loading.value = false;
    getList();
    ElMessage.success('删除成功');
  } catch (error: any) {
    loading.value = false;
    ElMessage.success(error);
  }
};
const handleRemove = () => {
  createReq.image = '';
};
const beforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
  let fileTypes = ['image/jpg', 'image/png', 'image/jpeg'];
  if (!fileTypes.includes(rawFile.type)) {
    ElMessage.warning('当前图片仅支持格式为：' + fileTypes.join(' ，'));
    return false;
  }
};
const upload = async (file) => {
  const formData = new FormData();
  // 添加要上传的文件名和文件对象,key就是你上传文件的字段名
  formData.append('img', file.file);
  let res = await uploadImg(formData);
  createReq.image = res.data;
};


//上传视频
const beforeVideoUpload: UploadProps['beforeUpload'] = (file) => {
  const isLt100M = file.size / 1024 / 1024 < 100;
  if (!isLt100M) {
    ElMessage.warning('上传文件大小不能超过 100M');
    return false;
  }
  return true;
};

const handleFileChange = async (file) => {
  // 判断是否是video类型
  if (file?.file.type.startsWith('video/')) {
    const formData = new FormData();
    // 添加要上传的文件名和文件对象,key就是你上传文件的字段名
    formData.append('video', file.file);
    let res = await uploadVideo(formData);
    createReq.jumpValue = '';
    nextTick(() => {
      createReq.jumpValue = res.data;
    });
  } else {
    alert('请选择一个视频文件！');
  }
};
const onSubmit = (formEl) => {
  formEl.validate(async (valid) => {
    if (!valid) {
      return;
    }
    try {
      if (fromType.value === 'ADD') {
        await bannerCreateApi(createReq);
        ElMessage.success('新增成功');
        nextTick(() => {
          dialogConfig.value = false;
          formEl.resetFields();
        });
        getList();
      } else if (fromType.value === 'EDIT') {
        await bannerUpdateApi(createReq);
        ElMessage.success('修改成功');
        nextTick(() => {
          dialogConfig.value = false;
          formEl.resetFields();
        });
        getList();
      }
    } catch (error: any) {
      ElMessage.error(error);
      throw new Error(error);
    }
  });
};
const onEditBanner = () => {
  bannerDialog.value = true;
};
const onBnnerSubmit = async () => {
  try {
    await bannerTitleUpdateApi(editBannerReq);
    ElMessage.success('修改成功');
    bannerDialog.value = false;
  } catch (error: any) {
    ElMessage.error(error);
    throw new Error(error);
  }
};
const dialogClose = () => {
  if (addForm.value) {
    addForm.value.resetFields();
  }
};

const reqQuestionListForm = reactive({
  zhanhuiGuid: '',
  position: 2,
  pageSize: 200,
  page: 1,
});
let questionList: any = ref([]);
//获取问题列表
const getQuestionList = async () => {
  loading.value = true;
  let goodsRes = await expoFaqListsApi(reqQuestionListForm);
  questionList.value = goodsRes.data.data;
};
onMounted(() => {
  reqListForm.zhanhuiGuid = route.query.guid as string;
  createReq.zhanhuiGuid = route.query.guid as string;
  editBannerReq.zhanhuiGuid = route.query.guid as string;
  reqQuestionListForm.zhanhuiGuid = route.query.guid as string;
  createReq.merchantGuid = route.query.merchantGuid as string;
  getList();
  getQuestionList();
});
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main v-loading="loading">
        <div class="hearder-box">
          <el-form :inline="true" :model="reqListForm" class="demo-form-inline">
            <el-form-item label="轮播图类型">
              <div class="mode-box">
                <div :class="['item', { active: reqListForm.bannerType === item.type }]"
                  v-for="(item, index) in bannerType" :key="index" @click="onChangeMode(item.type)">
                  {{ item.name }}
                </div>
              </div>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onAdd">新增</el-button>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onEditBanner">修改轮播标题</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-table :data="goodsList" border style="width: 100%">
          <el-table-column prop="sysId" label="sysId" width="80" />
          <el-table-column prop="title" label="轮播标题" />
          <el-table-column prop="image" label="轮播图片">
            <template #default="scope">
              <el-image style="width: 60px; height: 60px" :src="scope.row.image" />
            </template>
          </el-table-column>
          <el-table-column prop="jumpType" label="跳转类型">
            <template #default="scope">
              <span v-if="scope.row.jumpType == 1">不跳转</span>
              <span v-if="scope.row.jumpType == 2">跳转常见问题</span>
              <span v-if="scope.row.jumpType == 3">播放视频</span>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button size="small" type="primary" @click="onEdit(scope.row)">编辑</el-button>
              <el-popconfirm confirm-button-text="确认" cancel-button-text="取消" icon-color="red" title="是否删除该问题"
                @confirm="handleDelete(scope.row)">
                <!-- @cancel="cancelEvent" -->
                <template #reference>
                  <el-button size="small" type="danger">删除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </el-main>
      <el-footer>
        <el-pagination background layout="prev,pager, next" :total="total" :current-page="reqListForm.page"
          @current-change="handlePageChang" />
      </el-footer>
    </el-container>
    <el-dialog v-model="dialogConfig" title="创建/编辑轮播" width="1000px" @close="dialogClose()">
      <el-form ref="addForm" :model="createReq" class="demo-form-inline" label-width="120px" :rules="rules">
        <el-form-item label="轮播图类型" prop="bannerType">
          <el-select v-model="createReq.bannerType" placeholder="请选择">
            <el-option :label="item.name" :value="item.type" v-for="item in bannerType" :key="item.type" />
          </el-select>
        </el-form-item>
        <el-form-item label="跳转类型" prop="jumpType">
          <el-select v-model="createReq.jumpType" placeholder="请选择">
            <el-option :label="item.name" :value="item.type" v-for="item in jumpType" :key="item.type" />
          </el-select>
        </el-form-item>
        <el-form-item label="轮播标题" prop="title">
          <el-input v-model="createReq.title" placeholder="请输入轮播标题" />
        </el-form-item>
        <el-form-item label="轮播图内容" prop="content" v-if="createReq.bannerType === 2 || createReq.bannerType === 3">
          <el-input v-model="createReq.content" placeholder="轮播图内容" />
        </el-form-item>
        <el-form-item label="跳转值" prop="jumpValue" v-if="createReq.jumpType === 2">
          <el-select v-model="createReq.jumpValue" placeholder="请选择">
            <el-option :label="item.question" :value="item.guid" v-for="item in questionList" :key="item.guid" />
          </el-select>
        </el-form-item>
        <el-form-item label="跳转值" prop="jumpValue" v-if="createReq.jumpType === 3">
          <!-- <el-input v-model="createReq.jumpValue" placeholder="视频地址" /> -->
          <el-upload ref="uploadImgRef" class="avatar-uploader" :before-upload="beforeVideoUpload"
            :show-file-list="false" :http-request="handleFileChange">
            <div class="upload-video-box">
              <el-button size="small" type="primary">选择视频文件</el-button>
              <p class="el-upload__tip">请上传MP4文件并且大小不超过100M的视频文件</p>
              <video width="220" height="160" controls v-if="createReq.jumpValue">
                <source :src="createReq.jumpValue" type="video/mp4" />
                <source :src="createReq.jumpValue" type="video/ogg" />
                您的浏览器不支持 HTML5 video 标签。
              </video>
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="轮播图" prop="image" v-if="createReq.bannerType === 1">
          <el-upload ref="uploadImgRef" class="avatar-uploader" :before-upload="beforeUpload" :show-file-list="false"
            :http-request="upload">
            <div class="upload-img-box">
              <img v-if="createReq.image" :src="createReq.image" class="preview-img" />
              <div class="upload-btn" v-else>
                <el-icon size="30" color="#cdd0d6"><i-ep-Plus /></el-icon>
              </div>
              <div class="operate-box" v-show="createReq.image" @click.stop="handleRemove">
                <el-icon size="22" color="#ffffff"><i-ep-Delete /></el-icon>
              </div>
            </div>
            <template #tip>
              <div class="el-upload__tip" style="margin-top: 3px">请上传4:3比例的图片 如：220x165</div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item label="嘉宾轮播图" prop="image" v-if="createReq.bannerType === 2 || createReq.bannerType === 3">
          <el-upload ref="uploadLogoRef" class="avatar-uploader" :before-upload="beforeUpload" :show-file-list="false"
            :http-request="upload">
            <div class="upload-img-box">
              <img v-if="createReq.image" :src="createReq.image" class="preview-img" />
              <div class="upload-btn" v-else>
                <el-icon size="30" color="#cdd0d6"><i-ep-Plus /></el-icon>
              </div>
              <div class="operate-box" v-show="createReq.image" @click.stop="handleRemove">
                <el-icon size="22" color="#ffffff"><i-ep-Delete /></el-icon>
              </div>
            </div>
            <template #tip>
              <div class="el-upload__tip" style="margin-top: 3px">请上传1:1比例的图片 如：90x90</div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="createReq.sort" :min="1" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit(addForm)">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-dialog v-model="bannerDialog" title="自定义轮播标题" width="600px">
      <el-form ref="bannerForm" :model="editBannerReq" class="demo-form-inline" label-width="120px">
        <el-form-item label="轮播图类型" prop="bannerType">
          <el-select v-model="editBannerReq.bannerType" placeholder="请选择">
            <el-option :label="item.name" :value="item.type" v-for="item in bannerType" :key="item.type" />
          </el-select>
        </el-form-item>
        <el-form-item label="轮播标题" prop="title">
          <el-input v-model="editBannerReq.title" placeholder="请输入轮播标题" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onBnnerSubmit">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.mode-box {
  background-color: #eee;
  display: flex;
  padding: 4px 0px;
  border-radius: 6px;

  .item {
    background-color: #fff;
    padding: 0 10px;
    margin: 0 5px;
    border-radius: 6px;
    text-align: center;
    cursor: pointer;
    height: 26px;
    line-height: 26px;

    &.active {
      background-color: #409eff;
      color: #fff;
    }
  }
}

.avatar-uploader {
  margin-right: 20px;
}

.upload-img-box {
  width: 120px;
  height: 120px;
  border: 1px solid #cdd0d6;
  box-sizing: border-box;
  position: relative;
  border-radius: 6px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;

  .preview-img {
    width: 100%;
    height: 100%;
  }

  .operate-box {
    position: absolute;
    z-index: 9;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.5);
    visibility: hidden;
  }

  &:hover .operate-box {
    visibility: visible;
  }
}
</style>
