<script setup lang="ts">
import {
  getRobotlistApi,
  uploadImg,
  createRobotApi,
  editRobotApi,
  delRobotApi,
  getKnowLedgeListApi,
} from '@/api/index';

import type { FormInstance, FormRules, UploadInstance, UploadProps } from 'element-plus';

//获取知识库列表
const knowLedgeReq = reactive({
  merchantGuid: '',
  pageSize: 100,
  page: 1,
});
const knowLedgeList: any = ref([]);
const getKnowLedgeList = async () => {
  knowLedgeReq.merchantGuid = createReqFrom.merchantGuid;
  let res = await getKnowLedgeListApi(knowLedgeReq);
  knowLedgeList.value = res.data.data;
};
let robotList = ref([]);
const reqForm = reactive({
  limit: 10,
  page: 1,
  merchantGuid: '',
});
//分页
const total = ref(0);
const getRobotlist = async () => {
  let res = await getRobotlistApi(reqForm);
  total.value = res.data.total;
  robotList.value = res.data.data;
};
const handlePageChang = async (page) => {
  reqForm.page = page;
  getRobotlist();
};
let dialogVisible = ref(false);
//创建Ai好友
let createReqFrom = reactive({
  id: 0,
  merchantGuid: '',
  name: '',
  standing: '',
  signature: '',
  avatar: '',
  chat_expense: 0,
  add_expense: 0,
  sort: 0,
  rule: '',
  welcome: '',
  build_knowledge_guids: [],
  knowledge_use_params:
    "针对用户的问题，我为你提供了知识库内容，知识库内容中的score代表了匹配度，满分是0.99，分数大于0.3就必须高度重视'，越高越匹配，content代表了知识库的内容。请你参考知识库的内容进行回答，尽量使用我为你提供的知识库内容，知识库内容是：",
});
const onAddRole = () => {
  dialogVisible.value = true;
  createReqFrom;
  dialogtype.value = 'add';
  getKnowLedgeList();
};
const onEdit = (item) => {
  createReqFrom = Object.assign(createReqFrom, item);
  createReqFrom.merchantGuid = item.merchant_guid;
  getKnowLedgeList();
  dialogVisible.value = true;
  createReqFrom.id = item.id;
  dialogtype.value = 'edit';
};
let dialogtype = ref('');
const uploadImgRef = ref<UploadInstance>();
const beforeAvatarUpload: UploadProps['beforeUpload'] = (rawFile) => {
  let fileTypes = ['image/jpg', 'image/png', 'image/jpeg'];
  if (!fileTypes.includes(rawFile.type)) {
    ElMessage.warning('当前图片仅支持格式为：' + fileTypes.join(' ，'));
    return false;
  } else if (rawFile.size / 1024 / 1024 > 2) {
    ElMessage.error('图片必须小于2M');
    return false;
  }
  return true;
};
const upload = async (file) => {
  const formData = new FormData();
  // 添加要上传的文件名和文件对象,key就是你上传文件的字段名
  formData.append('img', file.file);
  let res = await uploadImg(formData);
  createReqFrom.avatar = res.data;
};
const handleRemove = () => {
  createReqFrom.avatar = '';
};
const addForm = ref<FormInstance>();
const rules = reactive<FormRules>({
  name: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
  standing: [{ required: true, message: '请输入角色身份', trigger: 'blur' }],
  signature: [{ required: true, message: '请输入个性签名', trigger: 'blur' }],
  avatar: [{ required: true, message: '请选上传头像', trigger: 'blur' }],
  rule: [{ required: true, message: '请选输入身份规则说明', trigger: 'blur' }],
  welcome: [{ required: true, message: '请选输入欢迎语', trigger: 'blur' }],
});
const onSubmit = async (formEl) => {
  formEl.validate(async (valid) => {
    if (!valid) {
      return;
    }
    try {
      if (dialogtype.value === 'add') {
        await createRobotApi(createReqFrom);
        ElMessage.success('新增成功');
      } else if (dialogtype.value === 'edit') {
        await editRobotApi(createReqFrom);
        ElMessage.success('修改成功');
      }
      nextTick(() => {
        formEl.resetFields();
        dialogVisible.value = false;
      });
      getRobotlist();
    } catch (error: any) {
      ElMessage.error(error);
      throw new Error(error);
    }
  });
};
const onDelete = async (item) => {
  let req = {
    id: item.id,
  };
  await delRobotApi(req);
  ElMessage.success('删除成功');
  getRobotlist();
};
// const onPopSelect = () => {
//   getKnowLedgeList();
//   createReqFrom.build_knowledge_guids = [];
// };
getRobotlist();
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main>
        <div class="header-box">
          <el-form :inline="true" class="demo-form-inline">
            <el-form-item><el-button type="primary" @click="onAddRole">新增Ai好友</el-button></el-form-item>
          </el-form>
        </div>
        <el-table :data="robotList" border style="width: 100%">
          <el-table-column prop="id" label="角色id" width="80" />
          <el-table-column prop="name" label="名称" width="150" />
          <el-table-column prop="standing" label="身份" width="150" />
          <el-table-column prop="signature" label="个性签名" width="180" />
          <el-table-column prop="avatar" :show-overflow-tooltip="true" label="头像" width="100">
            <template #default="scope">
              <el-image :src="scope.row.avatar" style="width: 50px; height: 50px; border-radius: 50%"></el-image>
            </template>
          </el-table-column>
          <el-table-column prop="chat_expense" label="聊天一次消耗点数" width="120" />
          <el-table-column prop="add_expense" label="添加好友消耗点数" width="120" />
          <el-table-column prop="rule" label="身份规则说明" :show-overflow-tooltip="true" width="180" />
          <el-table-column prop="welcome" label="欢迎语" width="180" />
          <el-table-column label="操作">
            <template #default="scope">
              <el-button size="small" type="primary" @click="onEdit(scope.row)">角色编辑</el-button>
              <el-button size="small" type="warning" @click="onDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-main>
      <el-footer>
        <el-pagination background layout="prev,pager, next" :total="total" :current-page="reqForm.page"
          @current-change="handlePageChang" />
      </el-footer>
    </el-container>
    <el-dialog v-model="dialogVisible" title="新增/编辑 角色" width="1000px">
      <el-form ref="addForm" :model="createReqFrom" class="demo-form-inline" label-width="100px" :rules="rules">
        <!-- <el-form-item label="所属商家" prop="merchantGuid">
          <el-select v-model="createReqFrom.merchantGuid" placeholder="请选择" :disabled="dialogtype === 'edit'">
            <el-option :label="item.merchantName" :value="item.guid" v-for="item in tenantList" :key="item.guid" />
          </el-select>
        </el-form-item> -->
        <el-form-item label="选择知识库" prop="build_knowledge_guids">
          <el-select v-model="createReqFrom.build_knowledge_guids" placeholder="请选择" multiple :multiple-limit="1">
            <el-option :label="item.knowledgeTitle" :value="item.guid" v-for="item in knowLedgeList" :key="item.guid" />
          </el-select>
        </el-form-item>
        <el-form-item label="知识库参数" prop="knowledge_use_params">
          <el-input v-model="createReqFrom.knowledge_use_params" />
        </el-form-item>
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="createReqFrom.name" placeholder="角色名称" />
        </el-form-item>
        <el-form-item label="角色身份" prop="standing">
          <el-input v-model="createReqFrom.standing" placeholder="角色身份" />
        </el-form-item>
        <el-form-item label="个性签名" prop="signature">
          <el-input v-model="createReqFrom.signature" placeholder="个性签名" />
        </el-form-item>
        <!-- <el-form-item label="当前头像" v-if="dialogtype === 'edit'">
          <img v-if="createReqFrom.avatar" :src="createReqFrom.avatar" class="edit-img" />
        </el-form-item> -->
        <el-form-item label="头像" prop="avatar">
          <el-upload ref="uploadImgRef" class="avatar-uploader" :before-upload="beforeAvatarUpload"
            :show-file-list="false" :http-request="upload">
            <div class="upload-img-box">
              <img v-if="createReqFrom.avatar" :src="createReqFrom.avatar" class="preview-img" />
              <div class="upload-btn" v-else>
                <el-icon size="30" color="#cdd0d6"><i-ep-Plus /></el-icon>
              </div>
              <div class="operate-box" v-show="createReqFrom.avatar">
                <el-icon size="22" color="#ffffff" @click.stop="handleRemove"><i-ep-Delete /></el-icon>
              </div>
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="聊天消耗点数" prop="chat_expense">
          <el-input-number v-model="createReqFrom.chat_expense" :min="0" />
        </el-form-item>
        <el-form-item label="添加好友消耗点数" prop="add_expense">
          <el-input-number v-model="createReqFrom.add_expense" :min="0" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="createReqFrom.sort" :min="0" />
        </el-form-item>
        <el-form-item label="提示语" prop="rule">
          <!-- <el-input type="textarea" :rows="2" v-model="createReqFrom.rule" placeholder="提示语" /> -->
          <v-md-editor v-model="createReqFrom.rule" height="400px"></v-md-editor>
        </el-form-item>
        <el-form-item label="欢迎语" prop="welcome">
          <el-input v-model="createReqFrom.welcome" placeholder="欢迎语" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit(addForm)">提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.header-box {
  margin-bottom: 18px;
}

.avatar-uploader {
  margin-right: 20px;
}

.upload-img-box {
  width: 120px;
  height: 120px;
  border: 1px solid #cdd0d6;
  box-sizing: border-box;
  position: relative;
  border-radius: 6px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;

  .preview-img {
    width: 100%;
    height: 100%;
  }

  .operate-box {
    position: absolute;
    z-index: 9;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.5);
    visibility: hidden;
  }

  &:hover .operate-box {
    visibility: visible;
  }
}
</style>
