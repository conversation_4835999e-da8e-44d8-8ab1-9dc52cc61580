<script setup lang="ts">
import { getTenantListApi, getImgOrderListApi } from '@/api/index';

let tenantList: any = ref([]);
//获取商户列表
const getTenantList = async () => {
  let res = await getTenantListApi();
  tenantList.value = res.data;
  getImgListReq.merchantGuid = res.data[0].guid;
  getImgOrderList();
};

const getImgListReq = reactive({
  merchantGuid: '',
  nickname: '',
  status: '',
  page: 1,
  pageSize: 9,
});
//分页
const total = ref(0);
const imgList = ref([]);
let loading = ref(false);
const getImgOrderList = async () => {
  loading.value = true;
  let res = await getImgOrderListApi(getImgListReq);
  total.value = res.data.total;
  res.data.data.forEach((item) => {
    if (item.imageResult.length === 0) {
      item.path = '';
    } else {
      item.path = item.imageResult[0];
    }
    // wait-等待执行；doing-正在执行；fail-执行失败；success执行成功
    switch (item.orderStatus) {
      case 'success':
        item.status = '执行成功';
        break;
      case 'fail':
        item.status = '执行失败';
        break;
      case 'wait':
        item.status = '等待执行';
        break;
      case 'doing':
        item.status = '正在执行';
        break;
    }
  });
  imgList.value = res.data.data;
  loading.value = false;
};
const handlePageChang = async (page) => {
  getImgListReq.page = page;
  getImgOrderList();
};
let modeStatus = ref('');
const modeList = reactive([
  {
    id: 0,
    type: 'wait',
    name: '等待执行',
  },
  {
    id: 1,
    type: 'doing',
    name: '正在执行',
  },
  {
    id: 2,
    type: 'fail',
    name: '执行失败',
  },
  {
    id: 3,
    type: 'success',
    name: '执行成功',
  },
]);
const onChangeMode = (value) => {
  modeStatus.value = value;
  getImgListReq.status = value;
};
//搜索
const onSearch = () => {
  getImgOrderList();
};
getImgOrderList();
// getTenantList();
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main>
        <div class="header-box">
          <el-form :inline="true" :model="getImgListReq" class="demo-form-inline">
            <!-- <el-form-item label="所属商家">
              <el-select v-model="getImgListReq.merchantGuid" placeholder="请选择">
                <el-option :label="item.merchantName" :value="item.guid" v-for="item in tenantList" :key="item.guid" />
              </el-select>
            </el-form-item> -->
            <el-form-item label="用户昵称">
              <el-input v-model="getImgListReq.nickname" placeholder="用户昵称" />
            </el-form-item>
            <el-form-item label="场景类型">
              <div class="mode-box">
                <div :class="['item', { active: modeStatus === item.type }]" v-for="(item, index) in modeList"
                  :key="index" @click="onChangeMode(item.type)">
                  {{ item.name }}
                </div>
              </div>
            </el-form-item>
            <el-form-item> <el-button type="primary" @click="onSearch">搜索</el-button></el-form-item>
          </el-form>
        </div>
        <el-table v-loading="loading" :data="imgList" border style="width: 100%">
          <el-table-column prop="sysId" label="id" width="80" />
          <el-table-column prop="orderNo" label="订单编号" width="150" />
          <el-table-column prop="userPrompt" :show-overflow-tooltip="true" label="提示词" />
          <el-table-column prop="path" label="图片" width="100">
            <template #default="scope">
              <el-image :src="scope.row.path" :initial-index="0" :preview-src-list="scope.row.imageResult" :z-index="9"
                :preview-teleported="true" style="width: 50px; height: 50px" />
            </template>
          </el-table-column>
          <el-table-column prop="imageNum" label="绘画数量" width="150" />
          <el-table-column prop="imageSize" label="图片尺寸" width="150" />
          <el-table-column prop="payPoint" label="支付金额" width="150" />
          <el-table-column prop="status" label="状态" width="150" />
          <el-table-column prop="modifyTime" label="时间" width="250" />
        </el-table>
      </el-main>
      <el-footer>
        <el-pagination background layout="prev,pager, next" :total="total" :current-page="getImgListReq.page"
          @current-change="handlePageChang" />
      </el-footer>
    </el-container>
  </div>
</template>

<style scoped lang="scss">
.mode-box {
  background-color: #eee;
  display: flex;
  padding: 4px 0px;
  border-radius: 6px;
  width: 400px;

  .item {
    background-color: #fff;
    //padding: 0 5px;
    margin: 0 5px;
    flex: 1;
    border-radius: 6px;
    text-align: center;
    cursor: pointer;
    height: 26px;
    line-height: 26px;

    &.active {
      background-color: #409eff;
      color: #fff;
    }
  }
}
</style>
