<script setup lang="ts">
import {
  getTenantListApi,
  getAiAgentCategoryApi,
  createAiAgentCategoryApi,
  updateAiAgentCategoryApi,
  deleteAiAgentCategoryApi
} from '@/api';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';


// 分页参数
const total = ref(0);
const loading = ref(false);

// 搜索筛选参数
const searchParams = reactive({
  merchantGuid: '',
  pageSize: 10,
  page: 1,
});

// 数据类型定义
interface CategoryItem {
  guid: string;
  status: number;
  sysId: number;
  merchantGuid: string;
  categoryName: string;
  categoryDesc: string;
  categoryIcon: string;
  sortOrder: number;
  createTime: string;
}

// 列表数据
let categoryList = ref<CategoryItem[]>([]);
// let tenantList: any = ref([]);

// 表单相关
const dialogVisible = ref(false);
const dialogTitle = ref('');
const formRef = ref<FormInstance>();
const actionType = ref('add'); // 'add' 或 'edit'

// 表单数据
const formData = reactive({
  guid: '',
  merchantGuid: '',
  categoryName: '',
  sortOrder: 1,
  status: 1
});

// 表单验证规则
const rules: FormRules = {
  merchantGuid: [{ required: true, message: '请选择商户', trigger: 'change' }],
  categoryName: [{ required: true, message: '请输入分类名称', trigger: 'blur' }],
  sortOrder: [{ required: true, message: '请输入排序', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
};

// 获取分类列表
const getList = async () => {
  loading.value = true;
  try {
    let res = await getAiAgentCategoryApi(searchParams);
    if (res.data && Array.isArray(res.data)) {
      // 直接数组格式
      categoryList.value = res.data;
      total.value = res.data.length;
    } else if (res.data && res.data.data) {
      // 分页格式
      categoryList.value = res.data.data;
      total.value = res.data.total;
    }
  } catch (error) {
    ElMessage.error('获取分类列表失败');
  } finally {
    loading.value = false;
  }
};

// 搜索功能
const handleSearch = () => {
  searchParams.page = 1;
  getList();
};

// 重置搜索
const handleReset = () => {
  searchParams.page = 1;
  getList();
};

// 分页切换
const handlePageChange = (page: number) => {
  searchParams.page = page;
  getList();
};

// 每页条数切换
const handleSizeChange = (size: number) => {
  searchParams.pageSize = size;
  searchParams.page = 1;
  getList();
};
// 新增分类
const onAdd = () => {
  actionType.value = 'add';
  dialogTitle.value = '新增分类';

  // 重置表单数据
  Object.assign(formData, {
    guid: '',
    merchantGuid: '',
    categoryName: '',
    sortOrder: 1,
    status: 1
  });

  dialogVisible.value = true;
};

// 编辑分类
const onEdit = (item: CategoryItem) => {
  actionType.value = 'edit';
  dialogTitle.value = '编辑分类';

  // 填充表单数据
  Object.assign(formData, {
    guid: item.guid,
    merchantGuid: item.merchantGuid,
    categoryName: item.categoryName,
    sortOrder: item.sortOrder,
    status: item.status
  });

  dialogVisible.value = true;
};

// 删除分类
const onDelete = async (item: CategoryItem) => {
  try {
    await deleteAiAgentCategoryApi({ guid: item.guid });
    ElMessage.success('删除成功');
    getList();
  } catch (error) {
    ElMessage.error('删除失败');
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (actionType.value === 'edit') {
          // 更新分类
          await updateAiAgentCategoryApi({
            guid: formData.guid,
            categoryName: formData.categoryName,
            sortOrder: formData.sortOrder,
            status: formData.status
          });
          ElMessage.success('更新成功');
        } else {
          // 创建分类
          await createAiAgentCategoryApi({
            merchantGuid: formData.merchantGuid,
            categoryName: formData.categoryName,
            sortOrder: formData.sortOrder,
            status: formData.status
          });
          ElMessage.success('创建成功');
        }

        dialogVisible.value = false;
        getList();
      } catch (error) {
        ElMessage.error(actionType.value === 'edit' ? '更新失败' : '创建失败');
      }
    }
  });
};
getList();
</script>

<template>
  <div>
    <el-container class="wrapper">
      <el-main>
        <!-- 搜索筛选区域 -->
        <div class="search-box">
          <el-form :inline="true" class="search-form">
            <el-form-item>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
              <el-button @click="handleReset">重置</el-button>
              <el-button type="success" @click="onAdd">新增分类</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 表格区域 -->
        <el-table :data="categoryList" border style="width: 100%" v-loading="loading">
          <el-table-column prop="sysId" label="ID" />
          <el-table-column prop="categoryName" label="分类名称" />
          <el-table-column prop="categoryDesc" label="分类描述" show-overflow-tooltip />
          <el-table-column prop="categoryIcon" label="分类图标" width="100">
            <template #default="scope">
              <el-image v-if="scope.row.categoryIcon" :src="scope.row.categoryIcon" style="width: 40px; height: 40px"
                fit="cover" />
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="sortOrder" label="排序" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                {{ scope.row.status === 1 ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" />
          <el-table-column fixed="right" label="操作" width="150">
            <template #default="scope">
              <el-button size="small" type="primary" @click="onEdit(scope.row)">编辑</el-button>
              <el-popconfirm title="确认删除该分类吗？" confirm-button-text="确定" cancel-button-text="取消"
                @confirm="onDelete(scope.row)">
                <template #reference>
                  <el-button size="small" type="danger">删除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <div class="pagination-container">
          <el-pagination v-model:current-page="searchParams.page" v-model:page-size="searchParams.pageSize"
            :page-sizes="[10, 20, 50, 100]" :total="total" layout="total, sizes, prev, pager, next, jumper"
            :prev-text="'上一页'" :next-text="'下一页'" @size-change="handleSizeChange" @current-change="handlePageChange" />
        </div>
      </el-main>
    </el-container>

    <!-- 分类表单对话框 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="600px">
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px">
        <!--<el-form-item label="所属商户" prop="merchantGuid" v-if="actionType === 'add'">
          <el-select v-model="formData.merchantGuid" placeholder="请选择商户" style="width: 100%">
            <el-option :label="item.merchantName" :value="item.guid" v-for="item in tenantList" :key="item.guid" />
          </el-select>
        </el-form-item>-->
        <el-form-item label="分类名称" prop="categoryName">
          <el-input v-model="formData.categoryName" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number v-model="formData.sortOrder" :min="0" :max="999" style="width: 100%" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="2">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.search-box {
  background: #f5f7fa;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.search-form {
  margin: 0;
}

.search-form .el-form-item {
  margin-bottom: 0;
}

.pagination-container {
  display: flex;
  justify-content: flex-start;
  margin-top: 20px;
  padding: 20px 0;
}
</style>
